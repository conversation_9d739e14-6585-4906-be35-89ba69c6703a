{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\dashboards\\\\ResponsableDashboard.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap, FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle, FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck, FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar, FaCertificate, FaClipboardList, FaQuestionCircle } from 'react-icons/fa';\nconst ResponsableDashboard = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    cours: 0,\n    matieres: 0,\n    groupes: 0,\n    etudiants: 0,\n    parents: 0,\n    filieres: 0,\n    utilisateurs: 0,\n    enseignants: 0,\n    classes: 0,\n    niveaux: 0,\n    absences_aujourdhui: 0,\n    retards_aujourdhui: 0,\n    devoirs_en_cours: 0,\n    quiz_actifs: 0,\n    factures_impayees: 0,\n    diplomes_annee: 0\n  });\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🔄 Chargement des statistiques du dashboard...');\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('📊 Réponse API stats:', response.data);\n      if (response.data.success) {\n        setStats(response.data.stats);\n\n        // Traiter les activités récentes\n        if (response.data.stats.activites_recentes) {\n          setRecentActivities(response.data.stats.activites_recentes);\n        }\n\n        // Générer des alertes basées sur les données\n        const newAlertes = [];\n        if (response.data.stats.factures_impayees > 0) {\n          newAlertes.push({\n            id: 1,\n            type: 'warning',\n            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,\n            priority: 'high'\n          });\n        }\n        if (response.data.stats.absences_aujourdhui > 10) {\n          newAlertes.push({\n            id: 2,\n            type: 'urgent',\n            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,\n            priority: 'high'\n          });\n        }\n        if (response.data.stats.retards_aujourdhui > 5) {\n          newAlertes.push({\n            id: 3,\n            type: 'info',\n            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,\n            priority: 'medium'\n          });\n        }\n        newAlertes.push({\n          id: 4,\n          type: 'success',\n          message: 'Système opérationnel - Toutes les données sont à jour',\n          priority: 'low'\n        });\n        setAlertes(newAlertes);\n        console.log('✅ Statistiques chargées avec succès');\n      } else {\n        throw new Error(response.data.error || 'Erreur lors du chargement des statistiques');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données:', error);\n      setError('Impossible de charger les statistiques. Vérifiez votre connexion.');\n\n      // Données de fallback en cas d'erreur\n      setStats({\n        cours: 0,\n        matieres: 0,\n        groupes: 0,\n        etudiants: 0,\n        parents: 0,\n        filieres: 0,\n        utilisateurs: 0,\n        enseignants: 0,\n        classes: 0,\n        niveaux: 0,\n        absences_aujourdhui: 0,\n        retards_aujourdhui: 0,\n        devoirs_en_cours: 0,\n        quiz_actifs: 0,\n        factures_impayees: 0,\n        diplomes_annee: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'inscription':\n        return '👤';\n      case 'note':\n        return '📝';\n      case 'absence':\n        return '⚠️';\n      case 'enseignant':\n        return '👨‍🏫';\n      default:\n        return '📢';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return '#dc3545';\n      case 'medium':\n        return '#ffc107';\n      case 'low':\n        return '#28a745';\n      default:\n        return '#6c757d';\n    }\n  };\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    header: {\n      marginBottom: '30px'\n    },\n    headerContent: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '15px'\n    },\n    headerActions: {\n      display: 'flex',\n      gap: '10px'\n    },\n    refreshButton: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      border: 'none',\n      padding: '10px 20px',\n      borderRadius: '8px',\n      cursor: 'pointer',\n      fontSize: '14px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      transition: 'background-color 0.3s ease'\n    },\n    errorBanner: {\n      backgroundColor: '#f8d7da',\n      color: '#721c24',\n      padding: '12px 20px',\n      borderRadius: '8px',\n      border: '1px solid #f5c6cb',\n      display: 'flex',\n      alignItems: 'center',\n      marginTop: '15px'\n    },\n    loadingContainer: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '400px',\n      textAlign: 'center'\n    },\n    loadingIcon: {\n      fontSize: '3rem',\n      color: '#007bff',\n      animation: 'spin 2s linear infinite',\n      marginBottom: '20px'\n    },\n    sectionHeader: {\n      marginBottom: '20px'\n    },\n    sectionTitle: {\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: '#333',\n      display: 'flex',\n      alignItems: 'center',\n      marginBottom: '10px'\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem'\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n      gap: '20px',\n      marginBottom: '40px'\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      border: '1px solid #e9ecef'\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '12px',\n      color: 'white'\n    },\n    statContent: {\n      flex: 1\n    },\n    statNumber: {\n      fontSize: '2.2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px'\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '1rem',\n      fontWeight: '500',\n      marginBottom: '3px'\n    },\n    statDescription: {\n      color: '#999',\n      fontSize: '0.85rem',\n      fontStyle: 'italic'\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px'\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px'\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem'\n    },\n    activityContent: {\n      flex: 1\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px'\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem'\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid'\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333'\n    }\n  };\n  const statCards = [\n  // Statistiques principales demandées\n  {\n    icon: FaBook,\n    number: stats.cours,\n    label: 'Cours',\n    color: '#007bff',\n    link: '/cours',\n    description: 'Total des cours disponibles'\n  }, {\n    icon: FaBookOpen,\n    number: stats.matieres,\n    label: 'Matières',\n    color: '#28a745',\n    link: '/matieres',\n    description: 'Matières enseignées'\n  }, {\n    icon: FaLayerGroup,\n    number: stats.groupes,\n    label: 'Groupes',\n    color: '#17a2b8',\n    link: '/groupes',\n    description: 'Groupes d\\'étudiants'\n  }, {\n    icon: FaGraduationCap,\n    number: stats.etudiants,\n    label: 'Étudiants',\n    color: '#6f42c1',\n    link: '/etudiants',\n    description: 'Étudiants inscrits'\n  }, {\n    icon: FaUserFriends,\n    number: stats.parents,\n    label: 'Parents',\n    color: '#fd7e14',\n    link: '/parents',\n    description: 'Parents d\\'étudiants'\n  }, {\n    icon: FaStream,\n    number: stats.filieres,\n    label: 'Filières',\n    color: '#e83e8c',\n    link: '/filieres',\n    description: 'Filières d\\'études'\n  }, {\n    icon: FaIdCard,\n    number: stats.utilisateurs,\n    label: 'Utilisateurs',\n    color: '#20c997',\n    link: '/utilisateurs',\n    description: 'Utilisateurs du système'\n  }, {\n    icon: FaChalkboardTeacher,\n    number: stats.enseignants,\n    label: 'Enseignants',\n    color: '#ffc107',\n    link: '/enseignants',\n    description: 'Professeurs actifs'\n  }];\n\n  // Statistiques secondaires pour le monitoring\n  const secondaryStats = [{\n    icon: FaUsers,\n    number: stats.classes,\n    label: 'Classes',\n    color: '#6c757d',\n    link: '/classes'\n  }, {\n    icon: FaExclamationTriangle,\n    number: stats.absences_aujourdhui,\n    label: \"Absences Aujourd'hui\",\n    color: '#dc3545',\n    link: '/absences'\n  }, {\n    icon: FaClock,\n    number: stats.retards_aujourdhui,\n    label: \"Retards Aujourd'hui\",\n    color: '#fd7e14',\n    link: '/retards'\n  }, {\n    icon: FaClipboardList,\n    number: stats.devoirs_en_cours,\n    label: \"Devoirs en Cours\",\n    color: '#17a2b8',\n    link: '/devoirs'\n  }, {\n    icon: FaQuestionCircle,\n    number: stats.quiz_actifs,\n    label: \"Quiz Actifs\",\n    color: '#6f42c1',\n    link: '/quiz'\n  }, {\n    icon: FaFileInvoiceDollar,\n    number: stats.factures_impayees,\n    label: \"Factures Impayées\",\n    color: '#dc3545',\n    link: '/factures'\n  }, {\n    icon: FaCertificate,\n    number: stats.diplomes_annee,\n    label: \"Diplômes Cette Année\",\n    color: '#28a745',\n    link: '/diplomes'\n  }];\n  const quickActions = [{\n    icon: FaUsers,\n    title: 'Gérer les Utilisateurs',\n    description: 'Ajouter, modifier ou supprimer des utilisateurs',\n    link: '/utilisateurs'\n  }, {\n    icon: FaChalkboardTeacher,\n    title: 'Gérer les Cours',\n    description: 'Planifier et organiser les cours',\n    link: '/cours'\n  }, {\n    icon: FaGraduationCap,\n    title: 'Gérer les Classes',\n    description: 'Créer et organiser les classes',\n    link: '/classes'\n  }, {\n    icon: FaCog,\n    title: 'Configuration',\n    description: 'Paramètres système et configuration',\n    link: '/roles'\n  }, {\n    icon: FaFileInvoiceDollar,\n    title: 'Gestion Financière',\n    description: 'Factures et paiements',\n    link: '/factures'\n  }, {\n    icon: FaCertificate,\n    title: 'Diplômes',\n    description: 'Gestion des diplômes',\n    link: '/diplomes'\n  }];\n  const handleRefresh = () => {\n    fetchDashboardData();\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.container,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.loadingContainer,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(FaSync, {\n      style: styles.loadingIcon,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 11\n      }\n    }), /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 11\n      }\n    }, \"Chargement des statistiques...\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 11\n      }\n    }, \"Veuillez patienter pendant que nous r\\xE9cup\\xE9rons les donn\\xE9es.\")));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.headerContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.welcomeText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaUserTie, {\n    style: {\n      marginRight: '15px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 15\n    }\n  }), \"Bienvenue, \", (user === null || user === void 0 ? void 0 : user.email) || 'Responsable'), /*#__PURE__*/React.createElement(\"p\", {\n    style: styles.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 13\n    }\n  }, \"Tableau de bord administrateur - Vue d'ensemble de l'\\xE9cole\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.headerActions,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleRefresh,\n    style: styles.refreshButton,\n    title: \"Actualiser les donn\\xE9es\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaSync, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 15\n    }\n  }), \" Actualiser\"))), error && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.errorBanner,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaTimes, {\n    style: {\n      marginRight: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 13\n    }\n  }), error)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.sectionHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.sectionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaChartBar, {\n    style: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 11\n    }\n  }), \"Statistiques Principales\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 7\n    }\n  }, statCards.map((stat, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: stat.link || '#',\n    style: {\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.statIcon,\n      backgroundColor: stat.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 17\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 17\n    }\n  }, stat.label), stat.description && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statDescription,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 19\n    }\n  }, stat.description)))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.sectionHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.sectionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaCalendarAlt, {\n    style: {\n      marginRight: '10px',\n      color: '#28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 11\n    }\n  }), \"Monitoring & Activit\\xE9\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }\n  }, secondaryStats.map((stat, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: stat.link || '#',\n    style: {\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-3px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.secondaryStatIcon,\n      color: stat.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 17\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 17\n    }\n  }, stat.label)))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.quickActionsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 7\n    }\n  }, quickActions.map((action, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: action.link,\n    style: styles.actionCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(action.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 13\n    }\n  }, action.title), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionDescription,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 13\n    }\n  }, action.description)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.contentGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaChartBar, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 13\n    }\n  }), \"Activit\\xE9s R\\xE9centes\"), recentActivities.map(activity => /*#__PURE__*/React.createElement(\"div\", {\n    key: activity.id,\n    style: styles.activityItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.activityIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 15\n    }\n  }, getActivityIcon(activity.type)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 17\n    }\n  }, activity.message), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityTime,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 17\n    }\n  }, activity.time))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 13\n    }\n  }), \"Alertes & Notifications\"), alertes.map(alerte => /*#__PURE__*/React.createElement(\"div\", {\n    key: alerte.id,\n    style: {\n      ...styles.alertItem,\n      borderLeftColor: getPriorityColor(alerte.priority),\n      backgroundColor: `${getPriorityColor(alerte.priority)}10`\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.alertMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 15\n    }\n  }, alerte.message))))));\n};\nexport default ResponsableDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "Link", "axios", "FaUserTie", "FaUsers", "FaChalkboardTeacher", "FaGraduationCap", "FaUserFriends", "FaChartBar", "FaCog", "FaCalendarAlt", "FaExclamationTriangle", "FaBook", "FaBookOpen", "FaLayerGroup", "FaUserGraduate", "FaUserCheck", "FaStream", "FaIdCard", "FaSync", "FaTimes", "FaClock", "FaFileInvoiceDollar", "FaCertificate", "FaClipboardList", "FaQuestionCircle", "ResponsableDashboard", "user", "stats", "setStats", "cours", "matieres", "groupes", "etudiants", "parents", "filieres", "utilisateurs", "enseignants", "classes", "niveaux", "absences_aujourdhui", "retards_aujourdhui", "devoirs_en_cours", "quiz_actifs", "factures_impayees", "diplomes_annee", "recentActivities", "setRecentActivities", "alertes", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "fetchDashboardData", "console", "log", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "success", "activites_recentes", "newAlertes", "push", "id", "type", "message", "priority", "Error", "getActivityIcon", "getPriorityColor", "styles", "container", "padding", "backgroundColor", "minHeight", "header", "marginBottom", "headerContent", "display", "justifyContent", "alignItems", "headerActions", "gap", "refreshButton", "color", "border", "borderRadius", "cursor", "fontSize", "transition", "errorBanner", "marginTop", "loadingContainer", "flexDirection", "textAlign", "loadingIcon", "animation", "section<PERSON><PERSON><PERSON>", "sectionTitle", "fontWeight", "welcomeText", "subtitle", "statsGrid", "gridTemplateColumns", "statCard", "boxShadow", "statIcon", "marginRight", "statContent", "flex", "statNumber", "statLabel", "statDescription", "fontStyle", "quickActionsGrid", "actionCard", "textDecoration", "actionIcon", "actionTitle", "actionDescription", "contentGrid", "card", "cardTitle", "cardIcon", "activityItem", "borderBottom", "activityIcon", "activityContent", "activityMessage", "activityTime", "alertItem", "borderLeft", "alertMessage", "statCards", "icon", "number", "label", "link", "description", "secondaryStats", "quickActions", "title", "handleRefresh", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "onClick", "map", "stat", "index", "key", "to", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "secondaryStatsGrid", "secondaryStatCard", "secondaryStatIcon", "secondaryStatContent", "secondaryStatNumber", "secondaryStatLabel", "action", "activity", "time", "alerte", "borderLeftColor"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/dashboards/ResponsableDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap,\n  FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle,\n  FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck,\n  FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar,\n  FaCertificate, FaClipboardList, FaQuestionCircle\n} from 'react-icons/fa';\n\nconst ResponsableDashboard = () => {\n  const { user } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    cours: 0,\n    matieres: 0,\n    groupes: 0,\n    etudiants: 0,\n    parents: 0,\n    filieres: 0,\n    utilisateurs: 0,\n    enseignants: 0,\n    classes: 0,\n    niveaux: 0,\n    absences_aujourdhui: 0,\n    retards_aujourdhui: 0,\n    devoirs_en_cours: 0,\n    quiz_actifs: 0,\n    factures_impayees: 0,\n    diplomes_annee: 0\n  });\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🔄 Chargement des statistiques du dashboard...');\n\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      console.log('📊 Réponse API stats:', response.data);\n\n      if (response.data.success) {\n        setStats(response.data.stats);\n\n        // Traiter les activités récentes\n        if (response.data.stats.activites_recentes) {\n          setRecentActivities(response.data.stats.activites_recentes);\n        }\n\n        // Générer des alertes basées sur les données\n        const newAlertes = [];\n\n        if (response.data.stats.factures_impayees > 0) {\n          newAlertes.push({\n            id: 1,\n            type: 'warning',\n            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,\n            priority: 'high'\n          });\n        }\n\n        if (response.data.stats.absences_aujourdhui > 10) {\n          newAlertes.push({\n            id: 2,\n            type: 'urgent',\n            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,\n            priority: 'high'\n          });\n        }\n\n        if (response.data.stats.retards_aujourdhui > 5) {\n          newAlertes.push({\n            id: 3,\n            type: 'info',\n            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,\n            priority: 'medium'\n          });\n        }\n\n        newAlertes.push({\n          id: 4,\n          type: 'success',\n          message: 'Système opérationnel - Toutes les données sont à jour',\n          priority: 'low'\n        });\n\n        setAlertes(newAlertes);\n\n        console.log('✅ Statistiques chargées avec succès');\n      } else {\n        throw new Error(response.data.error || 'Erreur lors du chargement des statistiques');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données:', error);\n      setError('Impossible de charger les statistiques. Vérifiez votre connexion.');\n\n      // Données de fallback en cas d'erreur\n      setStats({\n        cours: 0, matieres: 0, groupes: 0, etudiants: 0, parents: 0,\n        filieres: 0, utilisateurs: 0, enseignants: 0, classes: 0, niveaux: 0,\n        absences_aujourdhui: 0, retards_aujourdhui: 0, devoirs_en_cours: 0,\n        quiz_actifs: 0, factures_impayees: 0, diplomes_annee: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getActivityIcon = (type) => {\n    switch(type) {\n      case 'inscription': return '👤';\n      case 'note': return '📝';\n      case 'absence': return '⚠️';\n      case 'enseignant': return '👨‍🏫';\n      default: return '📢';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch(priority) {\n      case 'high': return '#dc3545';\n      case 'medium': return '#ffc107';\n      case 'low': return '#28a745';\n      default: return '#6c757d';\n    }\n  };\n\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh',\n    },\n    header: {\n      marginBottom: '30px',\n    },\n    headerContent: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '15px',\n    },\n    headerActions: {\n      display: 'flex',\n      gap: '10px',\n    },\n    refreshButton: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      border: 'none',\n      padding: '10px 20px',\n      borderRadius: '8px',\n      cursor: 'pointer',\n      fontSize: '14px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      transition: 'background-color 0.3s ease',\n    },\n    errorBanner: {\n      backgroundColor: '#f8d7da',\n      color: '#721c24',\n      padding: '12px 20px',\n      borderRadius: '8px',\n      border: '1px solid #f5c6cb',\n      display: 'flex',\n      alignItems: 'center',\n      marginTop: '15px',\n    },\n    loadingContainer: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '400px',\n      textAlign: 'center',\n    },\n    loadingIcon: {\n      fontSize: '3rem',\n      color: '#007bff',\n      animation: 'spin 2s linear infinite',\n      marginBottom: '20px',\n    },\n    sectionHeader: {\n      marginBottom: '20px',\n    },\n    sectionTitle: {\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: '#333',\n      display: 'flex',\n      alignItems: 'center',\n      marginBottom: '10px',\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem',\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n      gap: '20px',\n      marginBottom: '40px',\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      border: '1px solid #e9ecef',\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '12px',\n      color: 'white',\n    },\n    statContent: {\n      flex: 1,\n    },\n    statNumber: {\n      fontSize: '2.2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px',\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '1rem',\n      fontWeight: '500',\n      marginBottom: '3px',\n    },\n    statDescription: {\n      color: '#999',\n      fontSize: '0.85rem',\n      fontStyle: 'italic',\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit',\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px',\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px',\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff',\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem',\n    },\n    activityContent: {\n      flex: 1,\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px',\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem',\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid',\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n  };\n\n  const statCards = [\n    // Statistiques principales demandées\n    {\n      icon: FaBook,\n      number: stats.cours,\n      label: 'Cours',\n      color: '#007bff',\n      link: '/cours',\n      description: 'Total des cours disponibles'\n    },\n    {\n      icon: FaBookOpen,\n      number: stats.matieres,\n      label: 'Matières',\n      color: '#28a745',\n      link: '/matieres',\n      description: 'Matières enseignées'\n    },\n    {\n      icon: FaLayerGroup,\n      number: stats.groupes,\n      label: 'Groupes',\n      color: '#17a2b8',\n      link: '/groupes',\n      description: 'Groupes d\\'étudiants'\n    },\n    {\n      icon: FaGraduationCap,\n      number: stats.etudiants,\n      label: 'Étudiants',\n      color: '#6f42c1',\n      link: '/etudiants',\n      description: 'Étudiants inscrits'\n    },\n    {\n      icon: FaUserFriends,\n      number: stats.parents,\n      label: 'Parents',\n      color: '#fd7e14',\n      link: '/parents',\n      description: 'Parents d\\'étudiants'\n    },\n    {\n      icon: FaStream,\n      number: stats.filieres,\n      label: 'Filières',\n      color: '#e83e8c',\n      link: '/filieres',\n      description: 'Filières d\\'études'\n    },\n    {\n      icon: FaIdCard,\n      number: stats.utilisateurs,\n      label: 'Utilisateurs',\n      color: '#20c997',\n      link: '/utilisateurs',\n      description: 'Utilisateurs du système'\n    },\n    {\n      icon: FaChalkboardTeacher,\n      number: stats.enseignants,\n      label: 'Enseignants',\n      color: '#ffc107',\n      link: '/enseignants',\n      description: 'Professeurs actifs'\n    },\n  ];\n\n  // Statistiques secondaires pour le monitoring\n  const secondaryStats = [\n    {\n      icon: FaUsers,\n      number: stats.classes,\n      label: 'Classes',\n      color: '#6c757d',\n      link: '/classes'\n    },\n    {\n      icon: FaExclamationTriangle,\n      number: stats.absences_aujourdhui,\n      label: \"Absences Aujourd'hui\",\n      color: '#dc3545',\n      link: '/absences'\n    },\n    {\n      icon: FaClock,\n      number: stats.retards_aujourdhui,\n      label: \"Retards Aujourd'hui\",\n      color: '#fd7e14',\n      link: '/retards'\n    },\n    {\n      icon: FaClipboardList,\n      number: stats.devoirs_en_cours,\n      label: \"Devoirs en Cours\",\n      color: '#17a2b8',\n      link: '/devoirs'\n    },\n    {\n      icon: FaQuestionCircle,\n      number: stats.quiz_actifs,\n      label: \"Quiz Actifs\",\n      color: '#6f42c1',\n      link: '/quiz'\n    },\n    {\n      icon: FaFileInvoiceDollar,\n      number: stats.factures_impayees,\n      label: \"Factures Impayées\",\n      color: '#dc3545',\n      link: '/factures'\n    },\n    {\n      icon: FaCertificate,\n      number: stats.diplomes_annee,\n      label: \"Diplômes Cette Année\",\n      color: '#28a745',\n      link: '/diplomes'\n    },\n  ];\n\n  const quickActions = [\n    { icon: FaUsers, title: 'Gérer les Utilisateurs', description: 'Ajouter, modifier ou supprimer des utilisateurs', link: '/utilisateurs' },\n    { icon: FaChalkboardTeacher, title: 'Gérer les Cours', description: 'Planifier et organiser les cours', link: '/cours' },\n    { icon: FaGraduationCap, title: 'Gérer les Classes', description: 'Créer et organiser les classes', link: '/classes' },\n    { icon: FaCog, title: 'Configuration', description: 'Paramètres système et configuration', link: '/roles' },\n    { icon: FaFileInvoiceDollar, title: 'Gestion Financière', description: 'Factures et paiements', link: '/factures' },\n    { icon: FaCertificate, title: 'Diplômes', description: 'Gestion des diplômes', link: '/diplomes' },\n  ];\n\n  const handleRefresh = () => {\n    fetchDashboardData();\n  };\n\n  if (loading) {\n    return (\n      <div style={styles.container}>\n        <div style={styles.loadingContainer}>\n          <FaSync style={styles.loadingIcon} />\n          <h2>Chargement des statistiques...</h2>\n          <p>Veuillez patienter pendant que nous récupérons les données.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête amélioré */}\n      <div style={styles.header}>\n        <div style={styles.headerContent}>\n          <div>\n            <h1 style={styles.welcomeText}>\n              <FaUserTie style={{ marginRight: '15px', color: '#007bff' }} />\n              Bienvenue, {user?.email || 'Responsable'}\n            </h1>\n            <p style={styles.subtitle}>Tableau de bord administrateur - Vue d'ensemble de l'école</p>\n          </div>\n          <div style={styles.headerActions}>\n            <button\n              onClick={handleRefresh}\n              style={styles.refreshButton}\n              title=\"Actualiser les données\"\n            >\n              <FaSync /> Actualiser\n            </button>\n          </div>\n        </div>\n\n        {error && (\n          <div style={styles.errorBanner}>\n            <FaTimes style={{ marginRight: '10px' }} />\n            {error}\n          </div>\n        )}\n      </div>\n\n      {/* Statistiques principales */}\n      <div style={styles.sectionHeader}>\n        <h2 style={styles.sectionTitle}>\n          <FaChartBar style={{ marginRight: '10px', color: '#007bff' }} />\n          Statistiques Principales\n        </h2>\n      </div>\n\n      <div style={styles.statsGrid}>\n        {statCards.map((stat, index) => (\n          <Link\n            key={index}\n            to={stat.link || '#'}\n            style={{ textDecoration: 'none', color: 'inherit' }}\n          >\n            <div\n              style={styles.statCard}\n              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{...styles.statIcon, backgroundColor: stat.color}}>\n                <stat.icon />\n              </div>\n              <div style={styles.statContent}>\n                <div style={styles.statNumber}>{stat.number}</div>\n                <div style={styles.statLabel}>{stat.label}</div>\n                {stat.description && (\n                  <div style={styles.statDescription}>{stat.description}</div>\n                )}\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Statistiques secondaires */}\n      <div style={styles.sectionHeader}>\n        <h2 style={styles.sectionTitle}>\n          <FaCalendarAlt style={{ marginRight: '10px', color: '#28a745' }} />\n          Monitoring & Activité\n        </h2>\n      </div>\n\n      <div style={styles.secondaryStatsGrid}>\n        {secondaryStats.map((stat, index) => (\n          <Link\n            key={index}\n            to={stat.link || '#'}\n            style={{ textDecoration: 'none', color: 'inherit' }}\n          >\n            <div\n              style={styles.secondaryStatCard}\n              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-3px)'}\n              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{...styles.secondaryStatIcon, color: stat.color}}>\n                <stat.icon />\n              </div>\n              <div style={styles.secondaryStatContent}>\n                <div style={styles.secondaryStatNumber}>{stat.number}</div>\n                <div style={styles.secondaryStatLabel}>{stat.label}</div>\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Actions rapides */}\n      <div style={styles.quickActionsGrid}>\n        {quickActions.map((action, index) => (\n          <Link\n            key={index}\n            to={action.link}\n            style={styles.actionCard}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n          >\n            <div style={styles.actionIcon}>\n              <action.icon />\n            </div>\n            <div style={styles.actionTitle}>{action.title}</div>\n            <div style={styles.actionDescription}>{action.description}</div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Contenu principal */}\n      <div style={styles.contentGrid}>\n        {/* Activités récentes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaChartBar style={styles.cardIcon} />\n            Activités Récentes\n          </h2>\n          {recentActivities.map(activity => (\n            <div key={activity.id} style={styles.activityItem}>\n              <span style={styles.activityIcon}>\n                {getActivityIcon(activity.type)}\n              </span>\n              <div style={styles.activityContent}>\n                <div style={styles.activityMessage}>{activity.message}</div>\n                <div style={styles.activityTime}>{activity.time}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Alertes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaExclamationTriangle style={styles.cardIcon} />\n            Alertes & Notifications\n          </h2>\n          {alertes.map(alerte => (\n            <div \n              key={alerte.id} \n              style={{\n                ...styles.alertItem,\n                borderLeftColor: getPriorityColor(alerte.priority),\n                backgroundColor: `${getPriorityColor(alerte.priority)}10`\n              }}\n            >\n              <div style={styles.alertMessage}>{alerte.message}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResponsableDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,EACxDC,aAAa,EAAEC,UAAU,EAAEC,KAAK,EAAEC,aAAa,EAAEC,qBAAqB,EACtEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAC7DC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EACjEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAC3C,gBAAgB;AAEvB,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAG5B,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC;IACjCiC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,mBAAmB,EAAE,CAAC;IACtBC,kBAAkB,EAAE,CAAC;IACrBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdwD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM1D,KAAK,CAAC2D,GAAG,CAAC,gEAAgE,EAAE;QACjGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,QAAQ,CAACI,IAAI,CAAC;MAEnD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBpC,QAAQ,CAAC+B,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAAC;;QAE7B;QACA,IAAIgC,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACsC,kBAAkB,EAAE;UAC1CnB,mBAAmB,CAACa,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACsC,kBAAkB,CAAC;QAC7D;;QAEA;QACA,MAAMC,UAAU,GAAG,EAAE;QAErB,IAAIP,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACgB,iBAAiB,GAAG,CAAC,EAAE;UAC7CuB,UAAU,CAACC,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,GAAGX,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACgB,iBAAiB,oCAAoC;YACrF4B,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAIZ,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACY,mBAAmB,GAAG,EAAE,EAAE;UAChD2B,UAAU,CAACC,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE,wCAAwCX,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACY,mBAAmB,EAAE;YAC1FgC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAIZ,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACa,kBAAkB,GAAG,CAAC,EAAE;UAC9C0B,UAAU,CAACC,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,GAAGX,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACa,kBAAkB,mCAAmC;YACrF+B,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEAL,UAAU,CAACC,IAAI,CAAC;UACdC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,uDAAuD;UAChEC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEFvB,UAAU,CAACkB,UAAU,CAAC;QAEtBZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD,CAAC,MAAM;QACL,MAAM,IAAIiB,KAAK,CAACb,QAAQ,CAACI,IAAI,CAACZ,KAAK,IAAI,4CAA4C,CAAC;MACtF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEC,QAAQ,CAAC,mEAAmE,CAAC;;MAE7E;MACAxB,QAAQ,CAAC;QACPC,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAC3DC,QAAQ,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QACpEC,mBAAmB,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,gBAAgB,EAAE,CAAC;QAClEC,WAAW,EAAE,CAAC;QAAEC,iBAAiB,EAAE,CAAC;QAAEC,cAAc,EAAE;MACxD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,eAAe,GAAIJ,IAAI,IAAK;IAChC,QAAOA,IAAI;MACT,KAAK,aAAa;QAAE,OAAO,IAAI;MAC/B,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,SAAS;QAAE,OAAO,IAAI;MAC3B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAIH,QAAQ,IAAK;IACrC,QAAOA,QAAQ;MACb,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMI,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,YAAY,EAAE;IAChB,CAAC;IACDC,aAAa,EAAE;MACbC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpBJ,YAAY,EAAE;IAChB,CAAC;IACDK,aAAa,EAAE;MACbH,OAAO,EAAE,MAAM;MACfI,GAAG,EAAE;IACP,CAAC;IACDC,aAAa,EAAE;MACbV,eAAe,EAAE,SAAS;MAC1BW,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,MAAM;MACdb,OAAO,EAAE,WAAW;MACpBc,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,MAAM;MAChBV,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBE,GAAG,EAAE,KAAK;MACVO,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;MACXjB,eAAe,EAAE,SAAS;MAC1BW,KAAK,EAAE,SAAS;MAChBZ,OAAO,EAAE,WAAW;MACpBc,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE,mBAAmB;MAC3BP,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,SAAS,EAAE;IACb,CAAC;IACDC,gBAAgB,EAAE;MAChBd,OAAO,EAAE,MAAM;MACfe,aAAa,EAAE,QAAQ;MACvBb,UAAU,EAAE,QAAQ;MACpBD,cAAc,EAAE,QAAQ;MACxBL,SAAS,EAAE,OAAO;MAClBoB,SAAS,EAAE;IACb,CAAC;IACDC,WAAW,EAAE;MACXP,QAAQ,EAAE,MAAM;MAChBJ,KAAK,EAAE,SAAS;MAChBY,SAAS,EAAE,yBAAyB;MACpCpB,YAAY,EAAE;IAChB,CAAC;IACDqB,aAAa,EAAE;MACbrB,YAAY,EAAE;IAChB,CAAC;IACDsB,YAAY,EAAE;MACZV,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbN,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBJ,YAAY,EAAE;IAChB,CAAC;IACDwB,WAAW,EAAE;MACXZ,QAAQ,EAAE,MAAM;MAChBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACDyB,QAAQ,EAAE;MACRjB,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE;IACZ,CAAC;IACDc,SAAS,EAAE;MACTxB,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,sCAAsC;MAC3DrB,GAAG,EAAE,MAAM;MACXN,YAAY,EAAE;IAChB,CAAC;IACD4B,QAAQ,EAAE;MACR/B,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE,4BAA4B;MACvC3B,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBS,UAAU,EAAE,eAAe;MAC3BF,MAAM,EAAE,SAAS;MACjBF,MAAM,EAAE;IACV,CAAC;IACDqB,QAAQ,EAAE;MACRlB,QAAQ,EAAE,QAAQ;MAClBmB,WAAW,EAAE,MAAM;MACnBnC,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBF,KAAK,EAAE;IACT,CAAC;IACDwB,WAAW,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVtB,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACDmC,SAAS,EAAE;MACT3B,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,MAAM;MAChBW,UAAU,EAAE,KAAK;MACjBvB,YAAY,EAAE;IAChB,CAAC;IACDoC,eAAe,EAAE;MACf5B,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,SAAS;MACnByB,SAAS,EAAE;IACb,CAAC;IACDC,gBAAgB,EAAE;MAChBpC,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,sCAAsC;MAC3DrB,GAAG,EAAE,MAAM;MACXN,YAAY,EAAE;IAChB,CAAC;IACDuC,UAAU,EAAE;MACV1C,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE,4BAA4B;MACvCX,SAAS,EAAE,QAAQ;MACnBL,UAAU,EAAE,qBAAqB;MACjCF,MAAM,EAAE,SAAS;MACjB6B,cAAc,EAAE,MAAM;MACtBhC,KAAK,EAAE;IACT,CAAC;IACDiC,UAAU,EAAE;MACV7B,QAAQ,EAAE,MAAM;MAChBJ,KAAK,EAAE,SAAS;MAChBR,YAAY,EAAE;IAChB,CAAC;IACD0C,WAAW,EAAE;MACX9B,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACD2C,iBAAiB,EAAE;MACjBnC,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE;IACZ,CAAC;IACDgC,WAAW,EAAE;MACX1C,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,SAAS;MAC9BrB,GAAG,EAAE;IACP,CAAC;IACDuC,IAAI,EAAE;MACJhD,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE;IACb,CAAC;IACDiB,SAAS,EAAE;MACTlC,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE,MAAM;MACpBE,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE;IACd,CAAC;IACD2C,QAAQ,EAAE;MACRhB,WAAW,EAAE,MAAM;MACnBvB,KAAK,EAAE;IACT,CAAC;IACDwC,YAAY,EAAE;MACZpD,OAAO,EAAE,MAAM;MACfqD,YAAY,EAAE,gBAAgB;MAC9B/C,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE;IACd,CAAC;IACD8C,YAAY,EAAE;MACZnB,WAAW,EAAE,MAAM;MACnBnB,QAAQ,EAAE;IACZ,CAAC;IACDuC,eAAe,EAAE;MACflB,IAAI,EAAE;IACR,CAAC;IACDmB,eAAe,EAAE;MACf5C,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,QAAQ;MAClBZ,YAAY,EAAE;IAChB,CAAC;IACDqD,YAAY,EAAE;MACZ7C,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE;IACZ,CAAC;IACD0C,SAAS,EAAE;MACT1D,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,KAAK;MACnBV,YAAY,EAAE,MAAM;MACpBuD,UAAU,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACZjC,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE;IACT;EACF,CAAC;EAED,MAAMiD,SAAS,GAAG;EAChB;EACA;IACEC,IAAI,EAAEhI,MAAM;IACZiI,MAAM,EAAEjH,KAAK,CAACE,KAAK;IACnBgH,KAAK,EAAE,OAAO;IACdpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE/H,UAAU;IAChBgI,MAAM,EAAEjH,KAAK,CAACG,QAAQ;IACtB+G,KAAK,EAAE,UAAU;IACjBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE9H,YAAY;IAClB+H,MAAM,EAAEjH,KAAK,CAACI,OAAO;IACrB8G,KAAK,EAAE,SAAS;IAChBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAEtI,eAAe;IACrBuI,MAAM,EAAEjH,KAAK,CAACK,SAAS;IACvB6G,KAAK,EAAE,WAAW;IAClBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAErI,aAAa;IACnBsI,MAAM,EAAEjH,KAAK,CAACM,OAAO;IACrB4G,KAAK,EAAE,SAAS;IAChBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE3H,QAAQ;IACd4H,MAAM,EAAEjH,KAAK,CAACO,QAAQ;IACtB2G,KAAK,EAAE,UAAU;IACjBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE1H,QAAQ;IACd2H,MAAM,EAAEjH,KAAK,CAACQ,YAAY;IAC1B0G,KAAK,EAAE,cAAc;IACrBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAEvI,mBAAmB;IACzBwI,MAAM,EAAEjH,KAAK,CAACS,WAAW;IACzByG,KAAK,EAAE,aAAa;IACpBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE;EACf,CAAC,CACF;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB;IACEL,IAAI,EAAExI,OAAO;IACbyI,MAAM,EAAEjH,KAAK,CAACU,OAAO;IACrBwG,KAAK,EAAE,SAAS;IAChBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEjI,qBAAqB;IAC3BkI,MAAM,EAAEjH,KAAK,CAACY,mBAAmB;IACjCsG,KAAK,EAAE,sBAAsB;IAC7BpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEvH,OAAO;IACbwH,MAAM,EAAEjH,KAAK,CAACa,kBAAkB;IAChCqG,KAAK,EAAE,qBAAqB;IAC5BpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEpH,eAAe;IACrBqH,MAAM,EAAEjH,KAAK,CAACc,gBAAgB;IAC9BoG,KAAK,EAAE,kBAAkB;IACzBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEnH,gBAAgB;IACtBoH,MAAM,EAAEjH,KAAK,CAACe,WAAW;IACzBmG,KAAK,EAAE,aAAa;IACpBpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEtH,mBAAmB;IACzBuH,MAAM,EAAEjH,KAAK,CAACgB,iBAAiB;IAC/BkG,KAAK,EAAE,mBAAmB;IAC1BpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAErH,aAAa;IACnBsH,MAAM,EAAEjH,KAAK,CAACiB,cAAc;IAC5BiG,KAAK,EAAE,sBAAsB;IAC7BpD,KAAK,EAAE,SAAS;IAChBqD,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMG,YAAY,GAAG,CACnB;IAAEN,IAAI,EAAExI,OAAO;IAAE+I,KAAK,EAAE,wBAAwB;IAAEH,WAAW,EAAE,iDAAiD;IAAED,IAAI,EAAE;EAAgB,CAAC,EACzI;IAAEH,IAAI,EAAEvI,mBAAmB;IAAE8I,KAAK,EAAE,iBAAiB;IAAEH,WAAW,EAAE,kCAAkC;IAAED,IAAI,EAAE;EAAS,CAAC,EACxH;IAAEH,IAAI,EAAEtI,eAAe;IAAE6I,KAAK,EAAE,mBAAmB;IAAEH,WAAW,EAAE,gCAAgC;IAAED,IAAI,EAAE;EAAW,CAAC,EACtH;IAAEH,IAAI,EAAEnI,KAAK;IAAE0I,KAAK,EAAE,eAAe;IAAEH,WAAW,EAAE,qCAAqC;IAAED,IAAI,EAAE;EAAS,CAAC,EAC3G;IAAEH,IAAI,EAAEtH,mBAAmB;IAAE6H,KAAK,EAAE,oBAAoB;IAAEH,WAAW,EAAE,uBAAuB;IAAED,IAAI,EAAE;EAAY,CAAC,EACnH;IAAEH,IAAI,EAAErH,aAAa;IAAE4H,KAAK,EAAE,UAAU;IAAEH,WAAW,EAAE,sBAAsB;IAAED,IAAI,EAAE;EAAY,CAAC,CACnG;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1B9F,kBAAkB,CAAC,CAAC;EACtB,CAAC;EAED,IAAIJ,OAAO,EAAE;IACX,oBACEtD,KAAA,CAAAyJ,aAAA;MAAKC,KAAK,EAAE1E,MAAM,CAACC,SAAU;MAAA0E,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3BhK,KAAA,CAAAyJ,aAAA;MAAKC,KAAK,EAAE1E,MAAM,CAACsB,gBAAiB;MAAAqD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAClChK,KAAA,CAAAyJ,aAAA,CAAClI,MAAM;MAACmI,KAAK,EAAE1E,MAAM,CAACyB,WAAY;MAAAkD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eACrChK,KAAA,CAAAyJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,gCAAkC,CAAC,eACvChK,KAAA,CAAAyJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,sEAA8D,CAC9D,CACF,CAAC;EAEV;EAEA,oBACEhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACC,SAAU;IAAA0E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACK,MAAO;IAAAsE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACO,aAAc;IAAAoE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BhK,KAAA,CAAAyJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEhK,KAAA,CAAAyJ,aAAA;IAAIC,KAAK,EAAE1E,MAAM,CAAC8B,WAAY;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BhK,KAAA,CAAAyJ,aAAA,CAAClJ,SAAS;IAACmJ,KAAK,EAAE;MAAErC,WAAW,EAAE,MAAM;MAAEvB,KAAK,EAAE;IAAU,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACpD,EAAC,CAAAjI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkI,KAAK,KAAI,aACzB,CAAC,eACLjK,KAAA,CAAAyJ,aAAA;IAAGC,KAAK,EAAE1E,MAAM,CAAC+B,QAAS;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+DAA6D,CACrF,CAAC,eACNhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACW,aAAc;IAAAgE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BhK,KAAA,CAAAyJ,aAAA;IACES,OAAO,EAAEV,aAAc;IACvBE,KAAK,EAAE1E,MAAM,CAACa,aAAc;IAC5B0D,KAAK,EAAC,2BAAwB;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE9BhK,KAAA,CAAAyJ,aAAA,CAAClI,MAAM;IAAAoI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACJ,CACL,CACF,CAAC,EAELxG,KAAK,iBACJxD,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACoB,WAAY;IAAAuD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BhK,KAAA,CAAAyJ,aAAA,CAACjI,OAAO;IAACkI,KAAK,EAAE;MAAErC,WAAW,EAAE;IAAO,CAAE;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,EAC1CxG,KACE,CAEJ,CAAC,eAGNxD,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC2B,aAAc;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BhK,KAAA,CAAAyJ,aAAA;IAAIC,KAAK,EAAE1E,MAAM,CAAC4B,YAAa;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BhK,KAAA,CAAAyJ,aAAA,CAAC7I,UAAU;IAAC8I,KAAK,EAAE;MAAErC,WAAW,EAAE,MAAM;MAAEvB,KAAK,EAAE;IAAU,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAE9D,CACD,CAAC,eAENhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACgC,SAAU;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BjB,SAAS,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBrK,KAAA,CAAAyJ,aAAA,CAACpJ,IAAI;IACHiK,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEH,IAAI,CAACjB,IAAI,IAAI,GAAI;IACrBO,KAAK,EAAE;MAAE5B,cAAc,EAAE,MAAM;MAAEhC,KAAK,EAAE;IAAU,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpDhK,KAAA,CAAAyJ,aAAA;IACEC,KAAK,EAAE1E,MAAM,CAACkC,QAAS;IACvBsD,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,eAAgB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvEhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAG1E,MAAM,CAACoC,QAAQ;MAAEjC,eAAe,EAAEiF,IAAI,CAACtE;IAAK,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5DhK,KAAA,CAAAyJ,aAAA,CAACW,IAAI,CAACpB,IAAI;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACNhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACsC,WAAY;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACwC,UAAW;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEI,IAAI,CAACnB,MAAY,CAAC,eAClDjJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACyC,SAAU;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEI,IAAI,CAAClB,KAAW,CAAC,EAC/CkB,IAAI,CAAChB,WAAW,iBACfpJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC0C,eAAgB;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEI,IAAI,CAAChB,WAAiB,CAE1D,CACF,CACD,CACP,CACE,CAAC,eAGNpJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC2B,aAAc;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BhK,KAAA,CAAAyJ,aAAA;IAAIC,KAAK,EAAE1E,MAAM,CAAC4B,YAAa;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BhK,KAAA,CAAAyJ,aAAA,CAAC3I,aAAa;IAAC4I,KAAK,EAAE;MAAErC,WAAW,EAAE,MAAM;MAAEvB,KAAK,EAAE;IAAU,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAEjE,CACD,CAAC,eAENhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC6F,kBAAmB;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnCX,cAAc,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BrK,KAAA,CAAAyJ,aAAA,CAACpJ,IAAI;IACHiK,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEH,IAAI,CAACjB,IAAI,IAAI,GAAI;IACrBO,KAAK,EAAE;MAAE5B,cAAc,EAAE,MAAM;MAAEhC,KAAK,EAAE;IAAU,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpDhK,KAAA,CAAAyJ,aAAA;IACEC,KAAK,EAAE1E,MAAM,CAAC8F,iBAAkB;IAChCN,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,eAAgB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvEhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAG1E,MAAM,CAAC+F,iBAAiB;MAAEjF,KAAK,EAAEsE,IAAI,CAACtE;IAAK,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3DhK,KAAA,CAAAyJ,aAAA,CAACW,IAAI,CAACpB,IAAI;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACNhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACgG,oBAAqB;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtChK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACiG,mBAAoB;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEI,IAAI,CAACnB,MAAY,CAAC,eAC3DjJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACkG,kBAAmB;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEI,IAAI,CAAClB,KAAW,CACrD,CACF,CACD,CACP,CACE,CAAC,eAGNlJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC4C,gBAAiB;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCV,YAAY,CAACa,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC9BrK,KAAA,CAAAyJ,aAAA,CAACpJ,IAAI;IACHiK,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEY,MAAM,CAAChC,IAAK;IAChBO,KAAK,EAAE1E,MAAM,CAAC6C,UAAW;IACzB2C,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,eAAgB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvEhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC+C,UAAW;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BhK,KAAA,CAAAyJ,aAAA,CAAC0B,MAAM,CAACnC,IAAI;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACX,CAAC,eACNhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACgD,WAAY;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEmB,MAAM,CAAC5B,KAAW,CAAC,eACpDvJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACiD,iBAAkB;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEmB,MAAM,CAAC/B,WAAiB,CAC3D,CACP,CACE,CAAC,eAGNpJ,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACkD,WAAY;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7BhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACmD,IAAK;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBhK,KAAA,CAAAyJ,aAAA;IAAIC,KAAK,EAAE1E,MAAM,CAACoD,SAAU;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BhK,KAAA,CAAAyJ,aAAA,CAAC7I,UAAU;IAAC8I,KAAK,EAAE1E,MAAM,CAACqD,QAAS;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAEpC,CAAC,EACJ9G,gBAAgB,CAACiH,GAAG,CAACiB,QAAQ,iBAC5BpL,KAAA,CAAAyJ,aAAA;IAAKa,GAAG,EAAEc,QAAQ,CAAC3G,EAAG;IAACiF,KAAK,EAAE1E,MAAM,CAACsD,YAAa;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChDhK,KAAA,CAAAyJ,aAAA;IAAMC,KAAK,EAAE1E,MAAM,CAACwD,YAAa;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BlF,eAAe,CAACsG,QAAQ,CAAC1G,IAAI,CAC1B,CAAC,eACP1E,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACyD,eAAgB;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjChK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC0D,eAAgB;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEoB,QAAQ,CAACzG,OAAa,CAAC,eAC5D3E,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC2D,YAAa;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEoB,QAAQ,CAACC,IAAU,CAClD,CACF,CACN,CACE,CAAC,eAGNrL,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAACmD,IAAK;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBhK,KAAA,CAAAyJ,aAAA;IAAIC,KAAK,EAAE1E,MAAM,CAACoD,SAAU;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BhK,KAAA,CAAAyJ,aAAA,CAAC1I,qBAAqB;IAAC2I,KAAK,EAAE1E,MAAM,CAACqD,QAAS;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,2BAE/C,CAAC,EACJ5G,OAAO,CAAC+G,GAAG,CAACmB,MAAM,iBACjBtL,KAAA,CAAAyJ,aAAA;IACEa,GAAG,EAAEgB,MAAM,CAAC7G,EAAG;IACfiF,KAAK,EAAE;MACL,GAAG1E,MAAM,CAAC4D,SAAS;MACnB2C,eAAe,EAAExG,gBAAgB,CAACuG,MAAM,CAAC1G,QAAQ,CAAC;MAClDO,eAAe,EAAE,GAAGJ,gBAAgB,CAACuG,MAAM,CAAC1G,QAAQ,CAAC;IACvD,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFhK,KAAA,CAAAyJ,aAAA;IAAKC,KAAK,EAAE1E,MAAM,CAAC8D,YAAa;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEsB,MAAM,CAAC3G,OAAa,CACnD,CACN,CACE,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAe7C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}