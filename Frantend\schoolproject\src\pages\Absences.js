import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Absences.css'; // Réutilisation des styles

const Absences = () => {
    const { user } = useContext(AuthContext);
    const [absences, setAbsences] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingAbsence, setEditingAbsence] = useState(null);
    const [etudiants, setEtudiants] = useState([]);
    const [matieres, setMatieres] = useState([]);
    const [enseignants, setEnseignants] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        etudiant_id: '',
        matiere_id: '',
        enseignant_id: '',
        date_absence: '',
        justification: ''
    });

    useEffect(() => {
        fetchAbsences();
        if (user?.role === 'admin' || user?.role === 'enseignant') {
            fetchEtudiants();
            fetchMatieres();
            if (user?.role === 'admin') {
                fetchEnseignants();
            }
        }
    }, []);

    const fetchAbsences = async () => {
        try {
            console.log('🔄 Chargement des absences...');

            // Utiliser l'API officielle restructurée
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/absences/', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('🔍 DEBUG ABSENCES API Response:', response.data);
            console.log('🔍 Status:', response.status);
            console.log('🔍 Headers:', response.headers);

            // Vérifier si la réponse est un tableau
            if (Array.isArray(response.data)) {
                setAbsences(response.data);
                console.log('✅ Absences chargées:', response.data.length);
            } else if (response.data && response.data.error) {
                console.error('❌ Erreur API:', response.data.error);
                Swal.fire('Erreur API', response.data.error, 'error');
                setAbsences([]);
            } else {
                console.warn('⚠️ Format de réponse inattendu:', response.data);
                setAbsences([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des absences:', error);
            console.error('❌ Détails erreur:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                url: error.config?.url
            });

            // Message d'erreur plus détaillé
            let errorMessage = 'Impossible de charger les absences';
            if (error.response?.status === 404) {
                errorMessage = 'API non trouvée. Vérifiez que le serveur est démarré.';
            } else if (error.response?.status === 500) {
                errorMessage = 'Erreur serveur. Vérifiez les logs PHP.';
            } else if (error.code === 'NETWORK_ERROR') {
                errorMessage = 'Erreur réseau. Vérifiez la connexion au serveur.';
            }

            Swal.fire('Erreur', errorMessage, 'error');
            setAbsences([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            // TEMPORAIRE : Utiliser l'API sans authentification pour les tests
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');

            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);

            if (response.data.success) {
                setEtudiants(response.data.etudiants);
                console.log('✅ Étudiants chargés:', response.data.etudiants.length);
                console.log('📚 Premier étudiant:', response.data.etudiants[0]);
            } else {
                console.error('❌ Erreur API étudiants:', response.data.error);
                setEtudiants([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des étudiants:', error);
            console.error('❌ Détails erreur:', error.response?.data || error.message);
            setEtudiants([]);
        }
    };

    const fetchMatieres = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setMatieres(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des matières:', error);
        }
    };

    const fetchEnseignants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('🔍 DEBUG ENSEIGNANTS API Response:', response.data);

            if (response.data.success) {
                setEnseignants(response.data.enseignants);
                console.log('✅ Enseignants chargés:', response.data.enseignants.length);
            } else {
                console.error('❌ Erreur API enseignants:', response.data.error);
                setEnseignants([]);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des enseignants:', error);
            setEnseignants([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Debug : Vérifier les données avant envoi
        console.log('🔍 DEBUG SUBMIT:');
        console.log('FormData avant envoi:', formData);
        console.log('Type de etudiant_id:', typeof formData.etudiant_id);
        console.log('Valeur de etudiant_id:', formData.etudiant_id);
        console.log('Étudiants disponibles:', etudiants);
        console.log('Nombre d\'étudiants:', etudiants.length);
        console.log('Premier étudiant:', etudiants[0]);

        // Vérifier si les étudiants sont chargés
        if (etudiants.length === 0) {
            console.error('❌ Aucun étudiant chargé !');
            Swal.fire('Erreur', 'Aucun étudiant disponible. Veuillez recharger la page.', 'error');
            return;
        }
        console.log('Nombre d\'étudiants:', etudiants.length);

        // Validation des données avec messages détaillés
        if (!formData.etudiant_id || formData.etudiant_id === '') {
            console.error('❌ Erreur: etudiant_id vide ou undefined');
            console.log('FormData complet:', formData);
            Swal.fire('Erreur', 'Veuillez sélectionner un étudiant. Liste disponible: ' + etudiants.length + ' étudiant(s)', 'error');
            return;
        }

        if (!formData.date_absence || formData.date_absence === '') {
            console.error('❌ Erreur: date_absence vide ou undefined');
            Swal.fire('Erreur', 'Veuillez sélectionner une date', 'error');
            return;
        }

        // S'assurer que les IDs sont des nombres
        const cleanData = {
            etudiant_id: parseInt(formData.etudiant_id),
            matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,
            enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,
            date_absence: formData.date_absence,
            justification: formData.justification || null
        };

        console.log('🔍 Données nettoyées:', cleanData);

        try {
            // Utiliser l'API officielle restructurée
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/absences/';
            const method = editingAbsence ? 'PUT' : 'POST';
            const data = editingAbsence ? { ...cleanData, id: editingAbsence.id } : cleanData;

            console.log('🔍 Données finales envoyées:', data);

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            console.log('✅ Réponse serveur:', response.data);

            Swal.fire('Succès', `Absence ${editingAbsence ? 'modifiée' : 'enregistrée'} avec succès`, 'success');
            setShowModal(false);
            setEditingAbsence(null);
            resetForm();
            fetchAbsences();
        } catch (error) {
            console.error('❌ Erreur complète:', error);
            console.error('❌ Réponse serveur:', error.response?.data);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (absence) => {
        setEditingAbsence(absence);
        setFormData({
            etudiant_id: absence.etudiant_id,
            matiere_id: absence.matiere_id || '',
            enseignant_id: absence.enseignant_id || '',
            date_absence: absence.date_absence,
            justification: absence.justification || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                // Utiliser l'API officielle restructurée
                const token = localStorage.getItem('token');
                await axios.delete('http://localhost/Project_PFE/Backend/pages/absences/', {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    data: { id }
                });
                Swal.fire('Supprimé!', 'L\'absence a été supprimée.', 'success');
                fetchAbsences();
            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire('Erreur', 'Impossible de supprimer l\'absence', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            matiere_id: '',
            enseignant_id: '',
            date_absence: '',
            justification: ''
        });
    };

    // Fonctions de filtrage et pagination (comme les factures)
    const filteredAbsences = absences.filter(absence => {
        const matchesSearch = absence.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            absence.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            absence.nom_prenom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' ||
                            (statusFilter === 'justified' && absence.justification) ||
                            (statusFilter === 'unjustified' && !absence.justification);

        return matchesSearch && matchesStatus;
    });

    // Pagination
    const totalPages = Math.ceil(filteredAbsences.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentAbsences = filteredAbsences.slice(startIndex, startIndex + itemsPerPage);

    const getJustificationBadge = (justification) => {
        if (justification && justification.trim()) {
            return <span className="badge badge-success">Justifiée</span>;
        }
        return <span className="badge badge-danger">Non justifiée</span>;
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('fr-FR');
    };

    // Vérifier si l'utilisateur est admin ou enseignant
    const canManageAbsences = user?.role === 'admin' || user?.role === 'enseignant';

    // Debug pour vérifier les données
    console.log('🔍 DEBUG ABSENCES:');
    console.log('User:', user);
    console.log('User role:', user?.role);
    console.log('Can manage absences:', canManageAbsences);
    console.log('Absences data:', absences);
    console.log('Absences count:', absences.length);
    if (canManageAbsences) {
        console.log('📚 Étudiants disponibles:', etudiants.length);
        console.log('📖 Matières disponibles:', matieres.length);
        console.log('👨‍🏫 Enseignants disponibles:', enseignants.length);
    }

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des absences...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>📋 Gestion des Absences</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredAbsences.length} absence(s) trouvée(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    


                    {/* Bouton d'ajout visible seulement pour Admin et Enseignants */}
                    {canManageAbsences && (
                        <button
                            className="btn btn-primary add-button"
                            onClick={() => setShowModal(true)}
                            title="Ajouter une nouvelle absence - Enregistrer l'absence d'un étudiant avec date, matière et justification"
                        >
                            <img src="/plus.png" alt="Ajouter" />
                            <span>Nouvelle Absence</span>
                            <div className="button-info">
                                <small>📅 Date • 👤 Étudiant • 📚 Matière • 💬 Justification</small>
                            </div>
                        </button>
                    )}
                </div>
            </div>

            {/* Filtres et recherche (comme les factures) */}
            <div className="filters-section">
                <div className="search-filters">
                    <div className="search-box">
                        <input
                            type="text"
                            placeholder="🔍 Rechercher par étudiant, matière ou enseignant..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                        />
                    </div>
                    <div className="filter-group">
                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="filter-select"
                        >
                            <option value="all">📊 Tous les statuts</option>
                            <option value="justified">✅ Justifiées</option>
                            <option value="unjustified">❌ Non justifiées</option>
                        </select>
                    </div>
                </div>
                <div className="results-info">
                    <span className="total-count">
                        {filteredAbsences.length} absence(s) trouvée(s)
                    </span>
                </div>
            </div>

            <div className="factures-grid">
                {filteredAbsences.length === 0 ? (
                    <div className="no-data">
                        <img src="/attendance.png" alt="Aucune absence" />
                        <p>Aucune absence trouvée</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👤 Étudiant</th>
                                    <th>📚 Matière</th>
                                    
                                    <th>📅 Date d'absence</th>
                                    <th>📝 Statut</th>
                                    <th>💬 Justification</th>
                                    {canManageAbsences && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentAbsences.map((absence) => (
                                    <tr key={absence.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{absence.etudiant_nom}</strong>
                                                <small>{absence.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e8f5e8',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {absence.matiere_nom || '-'}
                                            </span>
                                        </td>
                                       
                                        <td>
                                            <strong style={{ color: '#dc3545', fontSize: '1em' }}>
                                                {formatDate(absence.date_absence)}
                                            </strong>
                                        </td>
                                        <td>{getJustificationBadge(absence.justification)}</td>
                                        <td>
                                            {absence.justification ? (
                                                <span
                                                    className="justification-text"
                                                    title={absence.justification}
                                                    style={{
                                                        display: 'inline-block',
                                                        maxWidth: '200px',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                        padding: '4px 8px',
                                                        backgroundColor: '#f8f9fa',
                                                        borderRadius: '4px',
                                                        fontSize: '0.9em'
                                                    }}
                                                >
                                                    {absence.justification}
                                                </span>
                                            ) : (
                                                <span style={{ color: '#6c757d', fontStyle: 'italic' }}>
                                                    Aucune justification
                                                </span>
                                            )}
                                        </td>
                                        {canManageAbsencesForced && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning edit-button"
                                                        onClick={() => handleEdit(absence)}
                                                        title={`Modifier l'absence de ${absence.etudiant_nom} du ${formatDate(absence.date_absence)}${absence.matiere_nom ? ` en ${absence.matiere_nom}` : ''}`}
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                        <span className="btn-text">Modifier</span>
                                                        <div className="btn-info">
                                                            <small>✏️ Éditer les détails</small>
                                                        </div>
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger delete-button"
                                                        onClick={() => handleDelete(absence.id)}
                                                        title={`Supprimer définitivement l'absence de ${absence.etudiant_nom} du ${formatDate(absence.date_absence)}${absence.matiere_nom ? ` en ${absence.matiere_nom}` : ''}`}
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                        <span className="btn-text">Supprimer</span>
                                                        <div className="btn-info">
                                                            <small>🗑️ Suppression définitive</small>
                                                        </div>
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}

                {/* Pagination (comme les factures) */}
                {filteredAbsences.length > itemsPerPage && (
                    <div className="pagination-container">
                        <div className="pagination-info">
                            Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, filteredAbsences.length)} sur {filteredAbsences.length} absences
                        </div>
                        <div className="pagination">
                            <button
                                className="pagination-btn"
                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                disabled={currentPage === 1}
                            >
                                ← Précédent
                            </button>

                            {[...Array(totalPages)].map((_, index) => {
                                const pageNumber = index + 1;
                                if (
                                    pageNumber === 1 ||
                                    pageNumber === totalPages ||
                                    (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                                ) {
                                    return (
                                        <button
                                            key={pageNumber}
                                            className={`pagination-btn ${currentPage === pageNumber ? 'active' : ''}`}
                                            onClick={() => setCurrentPage(pageNumber)}
                                        >
                                            {pageNumber}
                                        </button>
                                    );
                                } else if (
                                    pageNumber === currentPage - 2 ||
                                    pageNumber === currentPage + 2
                                ) {
                                    return <span key={pageNumber} className="pagination-ellipsis">...</span>;
                                }
                                return null;
                            })}

                            <button
                                className="pagination-btn"
                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                disabled={currentPage === totalPages}
                            >
                                Suivant →
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {/* Modal pour ajouter/modifier une absence */}
            {showModal && canManageAbsencesForced && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingAbsence ? 'Modifier l\'absence' : 'Nouvelle absence'}</h3>
                            <button 
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingAbsence(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => {
                                        console.log('🔍 SELECT CHANGE:');
                                        console.log('Valeur sélectionnée:', e.target.value);
                                        console.log('Type:', typeof e.target.value);
                                        console.log('Option sélectionnée:', e.target.selectedOptions[0]?.text);
                                        setFormData({...formData, etudiant_id: e.target.value});
                                    }}
                                    required
                                    disabled={editingAbsence}
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.etudiant_id || etudiant.id} value={etudiant.etudiant_id || etudiant.id}>
                                            {etudiant.nom} {etudiant.prenom ? ` ${etudiant.prenom}` : ''} - {etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe'}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Matière (optionnel)</label>
                                <select
                                    value={formData.matiere_id}
                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}
                                >
                                    <option value="">Sélectionner une matière</option>
                                    {matieres.map((matiere) => (
                                        <option key={matiere.id} value={matiere.id}>
                                            {matiere.nom}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            {user?.role === 'admin' && (
                                <div className="form-group">
                                    <label>Enseignant (optionnel)</label>
                                    <select
                                        value={formData.enseignant_id}
                                        onChange={(e) => setFormData({...formData, enseignant_id: e.target.value})}
                                    >
                                        <option value="">Sélectionner un enseignant</option>
                                        {enseignants.map((enseignant) => (
                                            <option key={enseignant.enseignant_id || enseignant.id} value={enseignant.enseignant_id || enseignant.id}>
                                                {enseignant.nom} {enseignant.prenom ? ` ${enseignant.prenom}` : ''} - {enseignant.specialite || 'Spécialité non définie'}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            )}
                            <div className="form-group">
                                <label>Date d'absence</label>
                                <input
                                    type="date"
                                    value={formData.date_absence}
                                    onChange={(e) => setFormData({...formData, date_absence: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Justification (optionnel)</label>
                                <textarea
                                    value={formData.justification}
                                    onChange={(e) => setFormData({...formData, justification: e.target.value})}
                                    placeholder="Motif de l'absence..."
                                    rows="3"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                            </div>
                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingAbsence ? 'Modifier' : 'Enregistrer'}
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingAbsence(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Absences;
