<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

try {
    // Récupérer toutes les statistiques en une seule fois
    $stats = [];
    
    // 1. Nombre de cours
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM cours");
    $stats['cours'] = $stmt->fetch()['count'];
    
    // 2. Nombre de matières
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM matieres");
    $stats['matieres'] = $stmt->fetch()['count'];
    
    // 3. Nombre de groupes
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM groupes");
    $stats['groupes'] = $stmt->fetch()['count'];
    
    // 4. Nombre d'étudiants
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM etudiants");
    $stats['etudiants'] = $stmt->fetch()['count'];
    
    // 5. Nombre de parents
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM parents");
    $stats['parents'] = $stmt->fetch()['count'];
    
    // 6. Nombre de filières
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM filieres");
    $stats['filieres'] = $stmt->fetch()['count'];
    
    // 7. Nombre d'utilisateurs
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM utilisateurs");
    $stats['utilisateurs'] = $stmt->fetch()['count'];
    
    // 8. Nombre d'enseignants
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM enseignants");
    $stats['enseignants'] = $stmt->fetch()['count'];
    
    // 9. Nombre de classes
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM classes");
    $stats['classes'] = $stmt->fetch()['count'];
    
    // 10. Nombre de niveaux
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM niveaux");
    $stats['niveaux'] = $stmt->fetch()['count'];
    
    // 11. Statistiques supplémentaires pour le dashboard
    // Absences aujourd'hui
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM absences WHERE DATE(date_absence) = CURDATE()");
    $stats['absences_aujourdhui'] = $stmt->fetch()['count'];
    
    // Retards aujourd'hui
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM retards WHERE DATE(date_retard) = CURDATE()");
    $stats['retards_aujourdhui'] = $stmt->fetch()['count'];
    
    // Devoirs en cours
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM devoirs WHERE date_limite >= CURDATE()");
    $stats['devoirs_en_cours'] = $stmt->fetch()['count'];
    
    // Quiz actifs
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM quiz WHERE statut = 'actif'");
    $stats['quiz_actifs'] = $stmt->fetch()['count'];
    
    // Factures impayées
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM factures WHERE statut = 'impayé'");
    $stats['factures_impayees'] = $stmt->fetch()['count'];
    
    // Diplômes délivrés cette année
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM diplomes WHERE YEAR(date_obtention) = YEAR(CURDATE())");
    $stats['diplomes_annee'] = $stmt->fetch()['count'];
    
    // Statistiques par rôle
    $stmt = $pdo->query("
        SELECT r.nom as role_nom, COUNT(u.id) as count 
        FROM roles r 
        LEFT JOIN utilisateurs u ON r.id = u.role_id 
        GROUP BY r.id, r.nom
    ");
    $stats['par_role'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Statistiques par filière
    $stmt = $pdo->query("
        SELECT f.nom as filiere_nom, COUNT(e.id) as count 
        FROM filieres f 
        LEFT JOIN classes c ON f.id = c.filiere_id 
        LEFT JOIN groupes g ON c.id = g.classe_id 
        LEFT JOIN etudiants e ON g.id = e.groupe_id 
        GROUP BY f.id, f.nom
    ");
    $stats['par_filiere'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Activités récentes (dernières 24h)
    $stmt = $pdo->query("
        SELECT 'absence' as type, CONCAT('Absence signalée pour ', u.nom) as message, 
               DATE_FORMAT(a.date_absence, '%H:%i') as time, a.date_absence as date_complete
        FROM absences a 
        JOIN etudiants e ON a.etudiant_id = e.id 
        JOIN utilisateurs u ON e.utilisateur_id = u.id 
        WHERE a.date_absence >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        
        UNION ALL
        
        SELECT 'retard' as type, CONCAT('Retard signalé pour ', u.nom) as message, 
               DATE_FORMAT(r.date_retard, '%H:%i') as time, r.date_retard as date_complete
        FROM retards r 
        JOIN etudiants e ON r.etudiant_id = e.id 
        JOIN utilisateurs u ON e.utilisateur_id = u.id 
        WHERE r.date_retard >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        
        UNION ALL
        
        SELECT 'cours' as type, CONCAT('Nouveau cours publié: ', c.titre) as message, 
               DATE_FORMAT(c.date_publication, '%H:%i') as time, c.date_publication as date_complete
        FROM cours c 
        WHERE c.date_publication >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        
        ORDER BY date_complete DESC 
        LIMIT 10
    ");
    $stats['activites_recentes'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
    ]);
}
?>
