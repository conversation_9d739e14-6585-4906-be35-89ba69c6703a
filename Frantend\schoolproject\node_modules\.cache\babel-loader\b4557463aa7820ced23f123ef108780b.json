{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\dashboards\\\\EtudiantDashboard.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { FaGraduationCap, FaCalendarAlt, FaClipboardList, FaChartLine, FaBookOpen, FaClock, FaExclamationTriangle, FaSync, FaUserClock, FaFileAlt } from 'react-icons/fa';\nimport axios from 'axios';\nconst EtudiantDashboard = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [emploiDuTemps, setEmploiDuTemps] = useState([]);\n  const [devoirs, setDevoirs] = useState([]);\n  const [notes, setNotes] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [etudiantInfo, setEtudiantInfo] = useState(null);\n  const [activitesRecentes, setActivitesRecentes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Rafraîchissement automatique toutes les 3 minutes\n    let interval;\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        console.log('🔄 Rafraîchissement automatique des données étudiant...');\n        fetchDashboardData();\n      }, 3 * 60 * 1000); // 3 minutes\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [autoRefresh]);\n  const fetchDashboardData = async () => {\n    try {\n      // Simuler des données pour le moment\n      setStats({\n        moyenneGenerale: 14.5,\n        devoirsEnCours: 5,\n        coursAujourdhui: 4,\n        absences: 2\n      });\n      setEmploiDuTemps([{\n        id: 1,\n        matiere: 'Mathématiques',\n        heure: '08:00-09:30',\n        salle: 'A101',\n        enseignant: 'M. Dupont'\n      }, {\n        id: 2,\n        matiere: 'Français',\n        heure: '10:00-11:30',\n        salle: 'B205',\n        enseignant: 'Mme Martin'\n      }, {\n        id: 3,\n        matiere: 'Histoire',\n        heure: '14:00-15:30',\n        salle: 'C301',\n        enseignant: 'M. Bernard'\n      }, {\n        id: 4,\n        matiere: 'Anglais',\n        heure: '15:45-17:15',\n        salle: 'D102',\n        enseignant: 'Ms Johnson'\n      }]);\n      setDevoirs([{\n        id: 1,\n        matiere: 'Mathématiques',\n        titre: 'Exercices sur les fonctions',\n        dateRendu: '2024-01-15',\n        statut: 'en_cours'\n      }, {\n        id: 2,\n        matiere: 'Français',\n        titre: 'Dissertation sur Molière',\n        dateRendu: '2024-01-18',\n        statut: 'en_cours'\n      }, {\n        id: 3,\n        matiere: 'Physique',\n        titre: 'TP sur la mécanique',\n        dateRendu: '2024-01-20',\n        statut: 'a_faire'\n      }]);\n      setNotes([{\n        id: 1,\n        matiere: 'Mathématiques',\n        note: 16,\n        coefficient: 2,\n        date: '2024-01-10'\n      }, {\n        id: 2,\n        matiere: 'Français',\n        note: 13,\n        coefficient: 3,\n        date: '2024-01-08'\n      }, {\n        id: 3,\n        matiere: 'Histoire',\n        note: 15,\n        coefficient: 2,\n        date: '2024-01-05'\n      }]);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  };\n  const getStatutColor = statut => {\n    switch (statut) {\n      case 'en_cours':\n        return '#ffc107';\n      case 'termine':\n        return '#28a745';\n      case 'a_faire':\n        return '#dc3545';\n      default:\n        return '#6c757d';\n    }\n  };\n  const getStatutText = statut => {\n    switch (statut) {\n      case 'en_cours':\n        return 'En cours';\n      case 'termine':\n        return 'Terminé';\n      case 'a_faire':\n        return 'À faire';\n      default:\n        return 'Inconnu';\n    }\n  };\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    header: {\n      marginBottom: '30px'\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem'\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease'\n    },\n    statIcon: {\n      fontSize: '2rem',\n      marginBottom: '10px',\n      color: '#007bff'\n    },\n    statNumber: {\n      fontSize: '1.8rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px'\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '30px',\n      marginBottom: '30px'\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    scheduleItem: {\n      padding: '15px',\n      borderLeft: '4px solid #007bff',\n      backgroundColor: '#f8f9ff',\n      marginBottom: '15px',\n      borderRadius: '5px'\n    },\n    scheduleHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '8px'\n    },\n    scheduleTitle: {\n      fontWeight: 'bold',\n      color: '#333'\n    },\n    scheduleTime: {\n      color: '#007bff',\n      fontSize: '0.9rem'\n    },\n    scheduleDetails: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    homeworkItem: {\n      padding: '15px',\n      border: '1px solid #eee',\n      borderRadius: '8px',\n      marginBottom: '15px'\n    },\n    homeworkHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '10px'\n    },\n    homeworkTitle: {\n      fontWeight: 'bold',\n      color: '#333'\n    },\n    homeworkStatus: {\n      padding: '4px 12px',\n      borderRadius: '20px',\n      fontSize: '0.8rem',\n      fontWeight: 'bold',\n      color: 'white'\n    },\n    homeworkDetails: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    gradeItem: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: '12px',\n      borderBottom: '1px solid #eee'\n    },\n    gradeSubject: {\n      fontWeight: 'bold',\n      color: '#333'\n    },\n    gradeValue: {\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: '#007bff'\n    },\n    fullWidthCard: {\n      gridColumn: '1 / -1'\n    }\n  };\n  const statCards = [{\n    icon: FaChartLine,\n    number: stats.moyenneGenerale + '/20',\n    label: 'Moyenne Générale',\n    color: '#28a745'\n  }, {\n    icon: FaClipboardList,\n    number: stats.devoirsEnCours,\n    label: 'Devoirs en Cours',\n    color: '#ffc107'\n  }, {\n    icon: FaCalendarAlt,\n    number: stats.coursAujourdhui,\n    label: \"Cours Aujourd'hui\",\n    color: '#007bff'\n  }, {\n    icon: FaClock,\n    number: stats.absences,\n    label: 'Absences',\n    color: '#dc3545'\n  }];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.welcomeText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaGraduationCap, {\n    style: {\n      marginRight: '15px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 11\n    }\n  }), \"Bienvenue, \", (user === null || user === void 0 ? void 0 : user.email) || 'Étudiant'), /*#__PURE__*/React.createElement(\"p\", {\n    style: styles.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }\n  }, \"Tableau de bord \\xE9tudiant - Suivez vos cours et vos r\\xE9sultats\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }\n  }, statCards.map((stat, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: index,\n    style: styles.statCard,\n    onMouseEnter: e => e.target.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.target.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 13\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 13\n    }\n  }, stat.label)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.contentGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaCalendarAlt, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 13\n    }\n  }), \"Emploi du Temps - Aujourd'hui\"), emploiDuTemps.map(cours => /*#__PURE__*/React.createElement(\"div\", {\n    key: cours.id,\n    style: styles.scheduleItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.scheduleHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.scheduleTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 17\n    }\n  }, cours.matiere), /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.scheduleTime,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 17\n    }\n  }, cours.heure)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.scheduleDetails,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 15\n    }\n  }, \"Salle: \", cours.salle, \" | Enseignant: \", cours.enseignant)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaClipboardList, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }\n  }), \"Devoirs \\xE0 Rendre\"), devoirs.map(devoir => /*#__PURE__*/React.createElement(\"div\", {\n    key: devoir.id,\n    style: styles.homeworkItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.homeworkHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.homeworkTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 17\n    }\n  }, devoir.titre), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      ...styles.homeworkStatus,\n      backgroundColor: getStatutColor(devoir.statut)\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 17\n    }\n  }, getStatutText(devoir.statut))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.homeworkDetails,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 15\n    }\n  }, devoir.matiere, \" | \\xC0 rendre le: \", devoir.dateRendu))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.card,\n      ...styles.fullWidthCard\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaBookOpen, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 11\n    }\n  }), \"Notes R\\xE9centes\"), notes.map(note => /*#__PURE__*/React.createElement(\"div\", {\n    key: note.id,\n    style: styles.gradeItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.gradeSubject,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 15\n    }\n  }, note.matiere), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 15\n    }\n  }, \"Coefficient: \", note.coefficient, \" | Date: \", note.date)), /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.gradeValue,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 13\n    }\n  }, note.note, \"/20\")))));\n};\nexport default EtudiantDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "FaGraduationCap", "FaCalendarAlt", "FaClipboardList", "FaChartLine", "FaBookOpen", "FaClock", "FaExclamationTriangle", "FaSync", "FaUserClock", "FaFileAlt", "axios", "EtudiantDashboard", "user", "emploiDuTemps", "setEmploiDuTemps", "devoirs", "setDevoirs", "notes", "setNotes", "stats", "setStats", "etudiantInfo", "setEtudiantInfo", "activitesRecentes", "setActivitesRecentes", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "autoRefresh", "setAutoRefresh", "fetchDashboardData", "interval", "setInterval", "console", "log", "clearInterval", "moyenneGenerale", "devoirsEnCours", "coursAujourdhui", "absences", "id", "matiere", "heure", "salle", "enseignant", "titre", "dateRendu", "statut", "note", "coefficient", "date", "getStatutColor", "getStatutText", "styles", "container", "padding", "backgroundColor", "minHeight", "header", "marginBottom", "welcomeText", "fontSize", "fontWeight", "color", "subtitle", "statsGrid", "display", "gridTemplateColumns", "gap", "statCard", "borderRadius", "boxShadow", "textAlign", "transition", "statIcon", "statNumber", "statLabel", "contentGrid", "card", "cardTitle", "alignItems", "cardIcon", "marginRight", "scheduleItem", "borderLeft", "<PERSON><PERSON><PERSON><PERSON>", "justifyContent", "scheduleTitle", "scheduleTime", "scheduleDetails", "homeworkItem", "border", "<PERSON><PERSON><PERSON><PERSON>", "homeworkTitle", "homeworkStatus", "homeworkDetails", "gradeItem", "borderBottom", "gradeSubject", "gradeValue", "fullWidthCard", "gridColumn", "statCards", "icon", "number", "label", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "map", "stat", "index", "key", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "cours", "devoir"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/dashboards/EtudiantDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { FaGraduationCap, FaCalendarAlt, FaClipboardList, FaChartLine, FaBookOpen, FaClock, FaExclamationTriangle, FaSync, FaUserClock, FaFileAlt } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst EtudiantDashboard = () => {\n  const { user } = useContext(AuthContext);\n  const [emploiDuTemps, setEmploiDuTemps] = useState([]);\n  const [devoirs, setDevoirs] = useState([]);\n  const [notes, setNotes] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [etudiantInfo, setEtudiantInfo] = useState(null);\n  const [activitesRecentes, setActivitesRecentes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Rafraîchissement automatique toutes les 3 minutes\n    let interval;\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        console.log('🔄 Rafraîchissement automatique des données étudiant...');\n        fetchDashboardData();\n      }, 3 * 60 * 1000); // 3 minutes\n    }\n\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [autoRefresh]);\n\n  const fetchDashboardData = async () => {\n    try {\n      // Simuler des données pour le moment\n      setStats({\n        moyenneGenerale: 14.5,\n        devoirsEnCours: 5,\n        coursAujourdhui: 4,\n        absences: 2\n      });\n\n      setEmploiDuTemps([\n        { id: 1, matiere: 'Mathématiques', heure: '08:00-09:30', salle: 'A101', enseignant: 'M. Dupont' },\n        { id: 2, matiere: 'Français', heure: '10:00-11:30', salle: 'B205', enseignant: 'Mme Martin' },\n        { id: 3, matiere: 'Histoire', heure: '14:00-15:30', salle: 'C301', enseignant: 'M. Bernard' },\n        { id: 4, matiere: 'Anglais', heure: '15:45-17:15', salle: 'D102', enseignant: 'Ms Johnson' }\n      ]);\n\n      setDevoirs([\n        { id: 1, matiere: 'Mathématiques', titre: 'Exercices sur les fonctions', dateRendu: '2024-01-15', statut: 'en_cours' },\n        { id: 2, matiere: 'Français', titre: 'Dissertation sur Molière', dateRendu: '2024-01-18', statut: 'en_cours' },\n        { id: 3, matiere: 'Physique', titre: 'TP sur la mécanique', dateRendu: '2024-01-20', statut: 'a_faire' }\n      ]);\n\n      setNotes([\n        { id: 1, matiere: 'Mathématiques', note: 16, coefficient: 2, date: '2024-01-10' },\n        { id: 2, matiere: 'Français', note: 13, coefficient: 3, date: '2024-01-08' },\n        { id: 3, matiere: 'Histoire', note: 15, coefficient: 2, date: '2024-01-05' }\n      ]);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  };\n\n  const getStatutColor = (statut) => {\n    switch(statut) {\n      case 'en_cours': return '#ffc107';\n      case 'termine': return '#28a745';\n      case 'a_faire': return '#dc3545';\n      default: return '#6c757d';\n    }\n  };\n\n  const getStatutText = (statut) => {\n    switch(statut) {\n      case 'en_cours': return 'En cours';\n      case 'termine': return 'Terminé';\n      case 'a_faire': return 'À faire';\n      default: return 'Inconnu';\n    }\n  };\n\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh',\n    },\n    header: {\n      marginBottom: '30px',\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem',\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n    },\n    statIcon: {\n      fontSize: '2rem',\n      marginBottom: '10px',\n      color: '#007bff',\n    },\n    statNumber: {\n      fontSize: '1.8rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px',\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '30px',\n      marginBottom: '30px',\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff',\n    },\n    scheduleItem: {\n      padding: '15px',\n      borderLeft: '4px solid #007bff',\n      backgroundColor: '#f8f9ff',\n      marginBottom: '15px',\n      borderRadius: '5px',\n    },\n    scheduleHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '8px',\n    },\n    scheduleTitle: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n    scheduleTime: {\n      color: '#007bff',\n      fontSize: '0.9rem',\n    },\n    scheduleDetails: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    homeworkItem: {\n      padding: '15px',\n      border: '1px solid #eee',\n      borderRadius: '8px',\n      marginBottom: '15px',\n    },\n    homeworkHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '10px',\n    },\n    homeworkTitle: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n    homeworkStatus: {\n      padding: '4px 12px',\n      borderRadius: '20px',\n      fontSize: '0.8rem',\n      fontWeight: 'bold',\n      color: 'white',\n    },\n    homeworkDetails: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    gradeItem: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n    },\n    gradeSubject: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n    gradeValue: {\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: '#007bff',\n    },\n    fullWidthCard: {\n      gridColumn: '1 / -1',\n    },\n  };\n\n  const statCards = [\n    { icon: FaChartLine, number: stats.moyenneGenerale + '/20', label: 'Moyenne Générale', color: '#28a745' },\n    { icon: FaClipboardList, number: stats.devoirsEnCours, label: 'Devoirs en Cours', color: '#ffc107' },\n    { icon: FaCalendarAlt, number: stats.coursAujourdhui, label: \"Cours Aujourd'hui\", color: '#007bff' },\n    { icon: FaClock, number: stats.absences, label: 'Absences', color: '#dc3545' },\n  ];\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête */}\n      <div style={styles.header}>\n        <h1 style={styles.welcomeText}>\n          <FaGraduationCap style={{ marginRight: '15px', color: '#007bff' }} />\n          Bienvenue, {user?.email || 'Étudiant'}\n        </h1>\n        <p style={styles.subtitle}>Tableau de bord étudiant - Suivez vos cours et vos résultats</p>\n      </div>\n\n      {/* Statistiques */}\n      <div style={styles.statsGrid}>\n        {statCards.map((stat, index) => (\n          <div \n            key={index} \n            style={styles.statCard}\n            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}\n          >\n            <div style={styles.statIcon}>\n              <stat.icon />\n            </div>\n            <div style={styles.statNumber}>{stat.number}</div>\n            <div style={styles.statLabel}>{stat.label}</div>\n          </div>\n        ))}\n      </div>\n\n      {/* Contenu principal */}\n      <div style={styles.contentGrid}>\n        {/* Emploi du temps */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaCalendarAlt style={styles.cardIcon} />\n            Emploi du Temps - Aujourd'hui\n          </h2>\n          {emploiDuTemps.map(cours => (\n            <div key={cours.id} style={styles.scheduleItem}>\n              <div style={styles.scheduleHeader}>\n                <span style={styles.scheduleTitle}>{cours.matiere}</span>\n                <span style={styles.scheduleTime}>{cours.heure}</span>\n              </div>\n              <div style={styles.scheduleDetails}>\n                Salle: {cours.salle} | Enseignant: {cours.enseignant}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Devoirs */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaClipboardList style={styles.cardIcon} />\n            Devoirs à Rendre\n          </h2>\n          {devoirs.map(devoir => (\n            <div key={devoir.id} style={styles.homeworkItem}>\n              <div style={styles.homeworkHeader}>\n                <span style={styles.homeworkTitle}>{devoir.titre}</span>\n                <span \n                  style={{\n                    ...styles.homeworkStatus, \n                    backgroundColor: getStatutColor(devoir.statut)\n                  }}\n                >\n                  {getStatutText(devoir.statut)}\n                </span>\n              </div>\n              <div style={styles.homeworkDetails}>\n                {devoir.matiere} | À rendre le: {devoir.dateRendu}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Notes récentes */}\n      <div style={{...styles.card, ...styles.fullWidthCard}}>\n        <h2 style={styles.cardTitle}>\n          <FaBookOpen style={styles.cardIcon} />\n          Notes Récentes\n        </h2>\n        {notes.map(note => (\n          <div key={note.id} style={styles.gradeItem}>\n            <div>\n              <span style={styles.gradeSubject}>{note.matiere}</span>\n              <div style={{ color: '#666', fontSize: '0.9rem' }}>\n                Coefficient: {note.coefficient} | Date: {note.date}\n              </div>\n            </div>\n            <span style={styles.gradeValue}>{note.note}/20</span>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default EtudiantDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACzK,OAAOC,KAAK,MAAM,OAAO;AAEzB,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAM;IAAEC;EAAK,CAAC,GAAGd,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACdoC,kBAAkB,CAAC,CAAC;;IAEpB;IACA,IAAIC,QAAQ;IACZ,IAAIH,WAAW,EAAE;MACfG,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtEJ,kBAAkB,CAAC,CAAC;MACtB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IACrB;IAEA,OAAO,MAAM;MACX,IAAIC,QAAQ,EAAEI,aAAa,CAACJ,QAAQ,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;EAEjB,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACAb,QAAQ,CAAC;QACPmB,eAAe,EAAE,IAAI;QACrBC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE,CAAC;QAClBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF5B,gBAAgB,CAAC,CACf;QAAE6B,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,eAAe;QAAEC,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAY,CAAC,EACjG;QAAEJ,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,UAAU;QAAEC,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAa,CAAC,EAC7F;QAAEJ,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,UAAU;QAAEC,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAa,CAAC,EAC7F;QAAEJ,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,SAAS;QAAEC,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAa,CAAC,CAC7F,CAAC;MAEF/B,UAAU,CAAC,CACT;QAAE2B,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,eAAe;QAAEI,KAAK,EAAE,6BAA6B;QAAEC,SAAS,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAW,CAAC,EACtH;QAAEP,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,UAAU;QAAEI,KAAK,EAAE,0BAA0B;QAAEC,SAAS,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAW,CAAC,EAC9G;QAAEP,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,UAAU;QAAEI,KAAK,EAAE,qBAAqB;QAAEC,SAAS,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAU,CAAC,CACzG,CAAC;MAEFhC,QAAQ,CAAC,CACP;QAAEyB,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,eAAe;QAAEO,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAa,CAAC,EACjF;QAAEV,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,UAAU;QAAEO,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAa,CAAC,EAC5E;QAAEV,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,UAAU;QAAEO,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAa,CAAC,CAC7E,CAAC;IACJ,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC;EAED,MAAM2B,cAAc,GAAIJ,MAAM,IAAK;IACjC,QAAOA,MAAM;MACX,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMK,aAAa,GAAIL,MAAM,IAAK;IAChC,QAAOA,MAAM;MACX,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMM,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,YAAY,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE;MACXC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDK,QAAQ,EAAE;MACRD,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDI,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDU,QAAQ,EAAE;MACRb,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRb,QAAQ,EAAE,MAAM;MAChBF,YAAY,EAAE,MAAM;MACpBI,KAAK,EAAE;IACT,CAAC;IACDY,UAAU,EAAE;MACVd,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDiB,SAAS,EAAE;MACTb,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDgB,WAAW,EAAE;MACXX,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,SAAS;MAC9BC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDmB,IAAI,EAAE;MACJtB,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE;IACb,CAAC;IACDQ,SAAS,EAAE;MACTlB,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfc,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRC,WAAW,EAAE,MAAM;MACnBnB,KAAK,EAAE;IACT,CAAC;IACDoB,YAAY,EAAE;MACZ5B,OAAO,EAAE,MAAM;MACf6B,UAAU,EAAE,mBAAmB;MAC/B5B,eAAe,EAAE,SAAS;MAC1BG,YAAY,EAAE,MAAM;MACpBW,YAAY,EAAE;IAChB,CAAC;IACDe,cAAc,EAAE;MACdnB,OAAO,EAAE,MAAM;MACfoB,cAAc,EAAE,eAAe;MAC/BN,UAAU,EAAE,QAAQ;MACpBrB,YAAY,EAAE;IAChB,CAAC;IACD4B,aAAa,EAAE;MACbzB,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDyB,YAAY,EAAE;MACZzB,KAAK,EAAE,SAAS;MAChBF,QAAQ,EAAE;IACZ,CAAC;IACD4B,eAAe,EAAE;MACf1B,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACD6B,YAAY,EAAE;MACZnC,OAAO,EAAE,MAAM;MACfoC,MAAM,EAAE,gBAAgB;MACxBrB,YAAY,EAAE,KAAK;MACnBX,YAAY,EAAE;IAChB,CAAC;IACDiC,cAAc,EAAE;MACd1B,OAAO,EAAE,MAAM;MACfoB,cAAc,EAAE,eAAe;MAC/BN,UAAU,EAAE,QAAQ;MACpBrB,YAAY,EAAE;IAChB,CAAC;IACDkC,aAAa,EAAE;MACb/B,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACD+B,cAAc,EAAE;MACdvC,OAAO,EAAE,UAAU;MACnBe,YAAY,EAAE,MAAM;MACpBT,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDgC,eAAe,EAAE;MACfhC,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDmC,SAAS,EAAE;MACT9B,OAAO,EAAE,MAAM;MACfoB,cAAc,EAAE,eAAe;MAC/BN,UAAU,EAAE,QAAQ;MACpBzB,OAAO,EAAE,MAAM;MACf0C,YAAY,EAAE;IAChB,CAAC;IACDC,YAAY,EAAE;MACZpC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDoC,UAAU,EAAE;MACVtC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDqC,aAAa,EAAE;MACbC,UAAU,EAAE;IACd;EACF,CAAC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAEvG,WAAW;IAAEwG,MAAM,EAAExF,KAAK,CAACoB,eAAe,GAAG,KAAK;IAAEqE,KAAK,EAAE,kBAAkB;IAAE1C,KAAK,EAAE;EAAU,CAAC,EACzG;IAAEwC,IAAI,EAAExG,eAAe;IAAEyG,MAAM,EAAExF,KAAK,CAACqB,cAAc;IAAEoE,KAAK,EAAE,kBAAkB;IAAE1C,KAAK,EAAE;EAAU,CAAC,EACpG;IAAEwC,IAAI,EAAEzG,aAAa;IAAE0G,MAAM,EAAExF,KAAK,CAACsB,eAAe;IAAEmE,KAAK,EAAE,mBAAmB;IAAE1C,KAAK,EAAE;EAAU,CAAC,EACpG;IAAEwC,IAAI,EAAErG,OAAO;IAAEsG,MAAM,EAAExF,KAAK,CAACuB,QAAQ;IAAEkE,KAAK,EAAE,UAAU;IAAE1C,KAAK,EAAE;EAAU,CAAC,CAC/E;EAED,oBACEvE,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACC,SAAU;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACK,MAAO;IAAAkD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBzH,KAAA,CAAAkH,aAAA;IAAIC,KAAK,EAAEtD,MAAM,CAACO,WAAY;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BzH,KAAA,CAAAkH,aAAA,CAAC7G,eAAe;IAAC8G,KAAK,EAAE;MAAEzB,WAAW,EAAE,MAAM;MAAEnB,KAAK,EAAE;IAAU,CAAE;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC1D,EAAC,CAAAxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyG,KAAK,KAAI,UACzB,CAAC,eACL1H,KAAA,CAAAkH,aAAA;IAAGC,KAAK,EAAEtD,MAAM,CAACW,QAAS;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oEAA+D,CACvF,CAAC,eAGNzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACY,SAAU;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BX,SAAS,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzB7H,KAAA,CAAAkH,aAAA;IACEY,GAAG,EAAED,KAAM;IACXV,KAAK,EAAEtD,MAAM,CAACgB,QAAS;IACvBkD,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACe,SAAS,GAAG,kBAAmB;IACnEC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACe,SAAS,GAAG,eAAgB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhEzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACqB,QAAS;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAkH,aAAA,CAACU,IAAI,CAACb,IAAI;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACNzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACsB,UAAW;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEG,IAAI,CAACZ,MAAY,CAAC,eAClDhH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACuB,SAAU;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEG,IAAI,CAACX,KAAW,CAC5C,CACN,CACE,CAAC,eAGNjH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACwB,WAAY;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7BzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACyB,IAAK;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBzH,KAAA,CAAAkH,aAAA;IAAIC,KAAK,EAAEtD,MAAM,CAAC0B,SAAU;IAAA6B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAkH,aAAA,CAAC5G,aAAa;IAAC6G,KAAK,EAAEtD,MAAM,CAAC4B,QAAS;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,iCAEvC,CAAC,EACJvG,aAAa,CAACyG,GAAG,CAACS,KAAK,iBACtBpI,KAAA,CAAAkH,aAAA;IAAKY,GAAG,EAAEM,KAAK,CAACpF,EAAG;IAACmE,KAAK,EAAEtD,MAAM,CAAC8B,YAAa;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7CzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACgC,cAAe;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCzH,KAAA,CAAAkH,aAAA;IAAMC,KAAK,EAAEtD,MAAM,CAACkC,aAAc;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEW,KAAK,CAACnF,OAAc,CAAC,eACzDjD,KAAA,CAAAkH,aAAA;IAAMC,KAAK,EAAEtD,MAAM,CAACmC,YAAa;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEW,KAAK,CAAClF,KAAY,CAClD,CAAC,eACNlD,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACoC,eAAgB;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAC3B,EAACW,KAAK,CAACjF,KAAK,EAAC,iBAAe,EAACiF,KAAK,CAAChF,UACvC,CACF,CACN,CACE,CAAC,eAGNpD,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACyB,IAAK;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBzH,KAAA,CAAAkH,aAAA;IAAIC,KAAK,EAAEtD,MAAM,CAAC0B,SAAU;IAAA6B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAkH,aAAA,CAAC3G,eAAe;IAAC4G,KAAK,EAAEtD,MAAM,CAAC4B,QAAS;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,uBAEzC,CAAC,EACJrG,OAAO,CAACuG,GAAG,CAACU,MAAM,iBACjBrI,KAAA,CAAAkH,aAAA;IAAKY,GAAG,EAAEO,MAAM,CAACrF,EAAG;IAACmE,KAAK,EAAEtD,MAAM,CAACqC,YAAa;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9CzH,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAACuC,cAAe;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCzH,KAAA,CAAAkH,aAAA;IAAMC,KAAK,EAAEtD,MAAM,CAACwC,aAAc;IAAAe,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEY,MAAM,CAAChF,KAAY,CAAC,eACxDrD,KAAA,CAAAkH,aAAA;IACEC,KAAK,EAAE;MACL,GAAGtD,MAAM,CAACyC,cAAc;MACxBtC,eAAe,EAAEL,cAAc,CAAC0E,MAAM,CAAC9E,MAAM;IAC/C,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAED7D,aAAa,CAACyE,MAAM,CAAC9E,MAAM,CACxB,CACH,CAAC,eACNvD,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAEtD,MAAM,CAAC0C,eAAgB;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCY,MAAM,CAACpF,OAAO,EAAC,qBAAgB,EAACoF,MAAM,CAAC/E,SACrC,CACF,CACN,CACE,CACF,CAAC,eAGNtD,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAGtD,MAAM,CAACyB,IAAI;MAAE,GAAGzB,MAAM,CAAC+C;IAAa,CAAE;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpDzH,KAAA,CAAAkH,aAAA;IAAIC,KAAK,EAAEtD,MAAM,CAAC0B,SAAU;IAAA6B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAkH,aAAA,CAACzG,UAAU;IAAC0G,KAAK,EAAEtD,MAAM,CAAC4B,QAAS;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,qBAEpC,CAAC,EACJnG,KAAK,CAACqG,GAAG,CAACnE,IAAI,iBACbxD,KAAA,CAAAkH,aAAA;IAAKY,GAAG,EAAEtE,IAAI,CAACR,EAAG;IAACmE,KAAK,EAAEtD,MAAM,CAAC2C,SAAU;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCzH,KAAA,CAAAkH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzH,KAAA,CAAAkH,aAAA;IAAMC,KAAK,EAAEtD,MAAM,CAAC6C,YAAa;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEjE,IAAI,CAACP,OAAc,CAAC,eACvDjD,KAAA,CAAAkH,aAAA;IAAKC,KAAK,EAAE;MAAE5C,KAAK,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAS,CAAE;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eACpC,EAACjE,IAAI,CAACC,WAAW,EAAC,WAAS,EAACD,IAAI,CAACE,IAC3C,CACF,CAAC,eACN1D,KAAA,CAAAkH,aAAA;IAAMC,KAAK,EAAEtD,MAAM,CAAC8C,UAAW;IAAAS,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEjE,IAAI,CAACA,IAAI,EAAC,KAAS,CACjD,CACN,CACE,CACF,CAAC;AAEV,CAAC;AAED,eAAexC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}