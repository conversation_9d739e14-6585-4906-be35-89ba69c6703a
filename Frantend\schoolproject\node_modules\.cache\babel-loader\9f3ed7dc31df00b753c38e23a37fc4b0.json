{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\dashboards\\\\EtudiantDashboard.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { FaGraduationCap, FaCalendarAlt, FaClipboardList, FaChartLine, FaBookOpen, FaClock, FaExclamationTriangle, FaSync, FaUserClock, FaFileAlt } from 'react-icons/fa';\nimport axios from 'axios';\nconst EtudiantDashboard = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [emploiDuTemps, setEmploiDuTemps] = useState([]);\n  const [devoirs, setDevoirs] = useState([]);\n  const [notes, setNotes] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [etudiantInfo, setEtudiantInfo] = useState(null);\n  const [activitesRecentes, setActivitesRecentes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Rafraîchissement automatique toutes les 3 minutes\n    const interval = setInterval(() => {\n      console.log('🔄 Rafraîchissement automatique des données étudiant...');\n      fetchDashboardData();\n    }, 3 * 60 * 1000); // 3 minutes\n\n    return () => clearInterval(interval);\n  }, []);\n  const fetchDashboardData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      if (!(user === null || user === void 0 ? void 0 : user.id)) {\n        throw new Error('Utilisateur non connecté');\n      }\n      const response = await axios.get(`http://localhost/Project_PFE/Backend/pages/dashboard/student_stats.php?user_id=${user.id}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token') || 'demo-token'}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success && response.data.stats && response.data.etudiant) {\n        // Définir les statistiques directement depuis la base de données\n        setStats(response.data.stats);\n        setEtudiantInfo(response.data.etudiant);\n\n        // Traiter les données dynamiques\n        if (response.data.notes_recentes) {\n          setNotes(response.data.notes_recentes);\n        }\n        if (response.data.devoirs_en_cours) {\n          setDevoirs(response.data.devoirs_en_cours.map(devoir => ({\n            ...devoir,\n            statut: devoir.jours_restants <= 0 ? 'expire' : devoir.jours_restants <= 2 ? 'urgent' : 'en_cours'\n          })));\n        }\n        if (response.data.emploi_du_temps_aujourdhui) {\n          setEmploiDuTemps(response.data.emploi_du_temps_aujourdhui.map(cours => ({\n            id: Math.random(),\n            matiere: cours.matiere_nom,\n            heure: `${cours.heure_debut}-${cours.heure_fin}`,\n            salle: cours.salle || 'Non définie',\n            enseignant: cours.enseignant_nom || 'Non défini'\n          })));\n        }\n        if (response.data.activites_recentes) {\n          setActivitesRecentes(response.data.activites_recentes);\n        }\n\n        // Mettre à jour l'heure de dernière mise à jour\n        setLastUpdate(new Date().toLocaleTimeString('fr-FR'));\n        console.log('✅ Données étudiant chargées dynamiquement depuis la base de données:', response.data);\n      } else {\n        throw new Error(response.data.error || 'Aucune donnée reçue du serveur');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données étudiant:', error);\n      setError(`Impossible de charger les données depuis la base de données: ${error.message}`);\n\n      // Ne pas définir de données de fallback - garder stats à null\n      setStats(null);\n      setEtudiantInfo(null);\n      setNotes([]);\n      setDevoirs([]);\n      setEmploiDuTemps([]);\n      setActivitesRecentes([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRefresh = () => {\n    console.log('🔄 Actualisation manuelle des données...');\n    fetchDashboardData();\n  };\n  const getStatutColor = statut => {\n    switch (statut) {\n      case 'en_cours':\n        return '#ffc107';\n      case 'termine':\n        return '#28a745';\n      case 'urgent':\n        return '#fd7e14';\n      case 'expire':\n        return '#dc3545';\n      case 'a_faire':\n        return '#17a2b8';\n      default:\n        return '#6c757d';\n    }\n  };\n  const getStatutText = statut => {\n    switch (statut) {\n      case 'en_cours':\n        return 'En cours';\n      case 'termine':\n        return 'Terminé';\n      case 'urgent':\n        return 'Urgent';\n      case 'expire':\n        return 'Expiré';\n      case 'a_faire':\n        return 'À faire';\n      default:\n        return 'Inconnu';\n    }\n  };\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    header: {\n      marginBottom: '30px'\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem'\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease'\n    },\n    statIcon: {\n      fontSize: '2rem',\n      marginBottom: '10px',\n      color: '#007bff'\n    },\n    statNumber: {\n      fontSize: '1.8rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px'\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '30px',\n      marginBottom: '30px'\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    scheduleItem: {\n      padding: '15px',\n      borderLeft: '4px solid #007bff',\n      backgroundColor: '#f8f9ff',\n      marginBottom: '15px',\n      borderRadius: '5px'\n    },\n    scheduleHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '8px'\n    },\n    scheduleTitle: {\n      fontWeight: 'bold',\n      color: '#333'\n    },\n    scheduleTime: {\n      color: '#007bff',\n      fontSize: '0.9rem'\n    },\n    scheduleDetails: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    homeworkItem: {\n      padding: '15px',\n      border: '1px solid #eee',\n      borderRadius: '8px',\n      marginBottom: '15px'\n    },\n    homeworkHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '10px'\n    },\n    homeworkTitle: {\n      fontWeight: 'bold',\n      color: '#333'\n    },\n    homeworkStatus: {\n      padding: '4px 12px',\n      borderRadius: '20px',\n      fontSize: '0.8rem',\n      fontWeight: 'bold',\n      color: 'white'\n    },\n    homeworkDetails: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    gradeItem: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: '12px',\n      borderBottom: '1px solid #eee'\n    },\n    gradeSubject: {\n      fontWeight: 'bold',\n      color: '#333'\n    },\n    gradeValue: {\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: '#007bff'\n    },\n    fullWidthCard: {\n      gridColumn: '1 / -1'\n    },\n    refreshButton: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      border: 'none',\n      padding: '8px 16px',\n      borderRadius: '5px',\n      cursor: 'pointer',\n      fontSize: '0.9rem',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '5px',\n      transition: 'background-color 0.3s ease'\n    },\n    headerActions: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '10px'\n    },\n    loadingContainer: {\n      textAlign: 'center',\n      padding: '60px 20px',\n      color: '#666'\n    },\n    errorContainer: {\n      textAlign: 'center',\n      padding: '40px 20px',\n      backgroundColor: '#f8d7da',\n      color: '#721c24',\n      borderRadius: '10px',\n      border: '1px solid #f5c6cb',\n      margin: '20px 0'\n    },\n    noDataContainer: {\n      textAlign: 'center',\n      padding: '40px',\n      backgroundColor: 'white',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      border: '2px dashed #dc3545',\n      gridColumn: '1 / -1'\n    }\n  };\n\n  // Générer les cartes de statistiques seulement si les données sont disponibles\n  const statCards = stats ? [{\n    icon: FaChartLine,\n    number: stats.moyenne_generale ? `${stats.moyenne_generale}/20` : 'N/A',\n    label: 'Moyenne Générale',\n    color: stats.moyenne_generale >= 15 ? '#28a745' : stats.moyenne_generale >= 10 ? '#ffc107' : '#dc3545',\n    subtitle: `${stats.nombre_notes || 0} note(s)`\n  }, {\n    icon: FaClipboardList,\n    number: stats.devoirs_en_cours || 0,\n    label: 'Devoirs en Cours',\n    color: '#ffc107',\n    subtitle: 'À rendre prochainement'\n  }, {\n    icon: FaCalendarAlt,\n    number: stats.cours_aujourdhui || 0,\n    label: \"Cours Aujourd'hui\",\n    color: '#007bff',\n    subtitle: 'Emploi du temps'\n  }, {\n    icon: FaUserClock,\n    number: stats.total_absences || 0,\n    label: 'Total Absences',\n    color: '#dc3545',\n    subtitle: `${stats.absences_ce_mois || 0} ce mois`\n  }, {\n    icon: FaClock,\n    number: stats.total_retards || 0,\n    label: 'Total Retards',\n    color: '#fd7e14',\n    subtitle: `${stats.retards_ce_mois || 0} ce mois`\n  }, {\n    icon: FaFileAlt,\n    number: stats.quiz_completes || 0,\n    label: 'Quiz Complétés',\n    color: '#6f42c1',\n    subtitle: 'Évaluations terminées'\n  }] : [];\n\n  // Affichage de chargement\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.container,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.loadingContainer,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(FaSync, {\n      style: {\n        fontSize: '3rem',\n        color: '#007bff',\n        animation: 'spin 1s linear infinite'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 11\n      }\n    }), /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 11\n      }\n    }, \"Chargement de vos donn\\xE9es...\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }\n    }, \"R\\xE9cup\\xE9ration des informations depuis la base de donn\\xE9es\")));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.welcomeText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaGraduationCap, {\n    style: {\n      marginRight: '15px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 15\n    }\n  }), \"Bienvenue, \", etudiantInfo ? `${etudiantInfo.prenom} ${etudiantInfo.nom}` : (user === null || user === void 0 ? void 0 : user.email) || 'Étudiant'), /*#__PURE__*/React.createElement(\"p\", {\n    style: styles.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 13\n    }\n  }, \"Tableau de bord \\xE9tudiant - Suivez vos cours et vos r\\xE9sultats\", lastUpdate && ` • Dernière mise à jour: ${lastUpdate}`, etudiantInfo && ` • ${etudiantInfo.classe_nom || 'Classe non définie'} - ${etudiantInfo.filiere_nom || 'Filière non définie'}`)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.headerActions,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleRefresh,\n    style: styles.refreshButton,\n    title: \"Actualiser les donn\\xE9es maintenant\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaSync, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 15\n    }\n  }), \" Actualiser\")))), error && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.errorContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    style: {\n      fontSize: '2rem',\n      marginBottom: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 11\n    }\n  }, \"Erreur de chargement\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 11\n    }\n  }, error), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleRefresh,\n    style: styles.refreshButton,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaSync, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 13\n    }\n  }), \" R\\xE9essayer\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }\n  }, statCards.map((stat, index) => /*#__PURE__*/React.createElement(\"div\", {\n    key: index,\n    style: styles.statCard,\n    onMouseEnter: e => e.target.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.target.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 13\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 13\n    }\n  }, stat.label)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.contentGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaCalendarAlt, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 13\n    }\n  }), \"Emploi du Temps - Aujourd'hui\"), emploiDuTemps.map(cours => /*#__PURE__*/React.createElement(\"div\", {\n    key: cours.id,\n    style: styles.scheduleItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.scheduleHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.scheduleTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 17\n    }\n  }, cours.matiere), /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.scheduleTime,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 17\n    }\n  }, cours.heure)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.scheduleDetails,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 15\n    }\n  }, \"Salle: \", cours.salle, \" | Enseignant: \", cours.enseignant)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaClipboardList, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 13\n    }\n  }), \"Devoirs \\xE0 Rendre\"), devoirs.map(devoir => /*#__PURE__*/React.createElement(\"div\", {\n    key: devoir.id,\n    style: styles.homeworkItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.homeworkHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.homeworkTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 17\n    }\n  }, devoir.titre), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      ...styles.homeworkStatus,\n      backgroundColor: getStatutColor(devoir.statut)\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 17\n    }\n  }, getStatutText(devoir.statut))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.homeworkDetails,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 15\n    }\n  }, devoir.matiere, \" | \\xC0 rendre le: \", devoir.dateRendu))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.card,\n      ...styles.fullWidthCard\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaBookOpen, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 11\n    }\n  }), \"Notes R\\xE9centes\"), notes.map(note => /*#__PURE__*/React.createElement(\"div\", {\n    key: note.id,\n    style: styles.gradeItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.gradeSubject,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 15\n    }\n  }, note.matiere), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 15\n    }\n  }, \"Coefficient: \", note.coefficient, \" | Date: \", note.date)), /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.gradeValue,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 13\n    }\n  }, note.note, \"/20\")))));\n};\nexport default EtudiantDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "FaGraduationCap", "FaCalendarAlt", "FaClipboardList", "FaChartLine", "FaBookOpen", "FaClock", "FaExclamationTriangle", "FaSync", "FaUserClock", "FaFileAlt", "axios", "EtudiantDashboard", "user", "emploiDuTemps", "setEmploiDuTemps", "devoirs", "setDevoirs", "notes", "setNotes", "stats", "setStats", "etudiantInfo", "setEtudiantInfo", "activitesRecentes", "setActivitesRecentes", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "fetchDashboardData", "interval", "setInterval", "console", "log", "clearInterval", "id", "Error", "response", "get", "headers", "localStorage", "getItem", "data", "success", "etudiant", "notes_recentes", "devoirs_en_cours", "map", "devoir", "statut", "jours_restants", "emploi_du_temps_aujourdhui", "cours", "Math", "random", "matiere", "matiere_nom", "heure", "heure_debut", "heure_fin", "salle", "enseignant", "enseignant_nom", "activites_recentes", "Date", "toLocaleTimeString", "message", "handleRefresh", "getStatutColor", "getStatutText", "styles", "container", "padding", "backgroundColor", "minHeight", "header", "marginBottom", "welcomeText", "fontSize", "fontWeight", "color", "subtitle", "statsGrid", "display", "gridTemplateColumns", "gap", "statCard", "borderRadius", "boxShadow", "textAlign", "transition", "statIcon", "statNumber", "statLabel", "contentGrid", "card", "cardTitle", "alignItems", "cardIcon", "marginRight", "scheduleItem", "borderLeft", "<PERSON><PERSON><PERSON><PERSON>", "justifyContent", "scheduleTitle", "scheduleTime", "scheduleDetails", "homeworkItem", "border", "<PERSON><PERSON><PERSON><PERSON>", "homeworkTitle", "homeworkStatus", "homeworkDetails", "gradeItem", "borderBottom", "gradeSubject", "gradeValue", "fullWidthCard", "gridColumn", "refreshButton", "cursor", "headerActions", "loadingContainer", "<PERSON><PERSON><PERSON><PERSON>", "margin", "noDataContainer", "statCards", "icon", "number", "moyenne_generale", "label", "nombre_notes", "cours_aujourdhui", "total_absences", "absences_ce_mois", "total_retards", "retards_ce_mois", "quiz_completes", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "animation", "prenom", "nom", "email", "classe_nom", "filiere_nom", "onClick", "title", "stat", "index", "key", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "titre", "dateRendu", "note", "coefficient", "date"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/dashboards/EtudiantDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { FaGraduationCap, FaCalendarAlt, FaClipboardList, FaChartLine, FaBookOpen, FaClock, FaExclamationTriangle, FaSync, FaUserClock, FaFileAlt } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst EtudiantDashboard = () => {\n  const { user } = useContext(AuthContext);\n  const [emploiDuTemps, setEmploiDuTemps] = useState([]);\n  const [devoirs, setDevoirs] = useState([]);\n  const [notes, setNotes] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [etudiantInfo, setEtudiantInfo] = useState(null);\n  const [activitesRecentes, setActivitesRecentes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Rafraîchissement automatique toutes les 3 minutes\n    const interval = setInterval(() => {\n      console.log('🔄 Rafraîchissement automatique des données étudiant...');\n      fetchDashboardData();\n    }, 3 * 60 * 1000); // 3 minutes\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchDashboardData = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      if (!user?.id) {\n        throw new Error('Utilisateur non connecté');\n      }\n\n      const response = await axios.get(\n        `http://localhost/Project_PFE/Backend/pages/dashboard/student_stats.php?user_id=${user.id}`,\n        {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token') || 'demo-token'}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data.success && response.data.stats && response.data.etudiant) {\n        // Définir les statistiques directement depuis la base de données\n        setStats(response.data.stats);\n        setEtudiantInfo(response.data.etudiant);\n\n        // Traiter les données dynamiques\n        if (response.data.notes_recentes) {\n          setNotes(response.data.notes_recentes);\n        }\n\n        if (response.data.devoirs_en_cours) {\n          setDevoirs(response.data.devoirs_en_cours.map(devoir => ({\n            ...devoir,\n            statut: devoir.jours_restants <= 0 ? 'expire' :\n                   devoir.jours_restants <= 2 ? 'urgent' : 'en_cours'\n          })));\n        }\n\n        if (response.data.emploi_du_temps_aujourdhui) {\n          setEmploiDuTemps(response.data.emploi_du_temps_aujourdhui.map(cours => ({\n            id: Math.random(),\n            matiere: cours.matiere_nom,\n            heure: `${cours.heure_debut}-${cours.heure_fin}`,\n            salle: cours.salle || 'Non définie',\n            enseignant: cours.enseignant_nom || 'Non défini'\n          })));\n        }\n\n        if (response.data.activites_recentes) {\n          setActivitesRecentes(response.data.activites_recentes);\n        }\n\n        // Mettre à jour l'heure de dernière mise à jour\n        setLastUpdate(new Date().toLocaleTimeString('fr-FR'));\n\n        console.log('✅ Données étudiant chargées dynamiquement depuis la base de données:', response.data);\n      } else {\n        throw new Error(response.data.error || 'Aucune donnée reçue du serveur');\n      }\n\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données étudiant:', error);\n      setError(`Impossible de charger les données depuis la base de données: ${error.message}`);\n\n      // Ne pas définir de données de fallback - garder stats à null\n      setStats(null);\n      setEtudiantInfo(null);\n      setNotes([]);\n      setDevoirs([]);\n      setEmploiDuTemps([]);\n      setActivitesRecentes([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = () => {\n    console.log('🔄 Actualisation manuelle des données...');\n    fetchDashboardData();\n  };\n\n  const getStatutColor = (statut) => {\n    switch(statut) {\n      case 'en_cours': return '#ffc107';\n      case 'termine': return '#28a745';\n      case 'urgent': return '#fd7e14';\n      case 'expire': return '#dc3545';\n      case 'a_faire': return '#17a2b8';\n      default: return '#6c757d';\n    }\n  };\n\n  const getStatutText = (statut) => {\n    switch(statut) {\n      case 'en_cours': return 'En cours';\n      case 'termine': return 'Terminé';\n      case 'urgent': return 'Urgent';\n      case 'expire': return 'Expiré';\n      case 'a_faire': return 'À faire';\n      default: return 'Inconnu';\n    }\n  };\n\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh',\n    },\n    header: {\n      marginBottom: '30px',\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem',\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n    },\n    statIcon: {\n      fontSize: '2rem',\n      marginBottom: '10px',\n      color: '#007bff',\n    },\n    statNumber: {\n      fontSize: '1.8rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px',\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '30px',\n      marginBottom: '30px',\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff',\n    },\n    scheduleItem: {\n      padding: '15px',\n      borderLeft: '4px solid #007bff',\n      backgroundColor: '#f8f9ff',\n      marginBottom: '15px',\n      borderRadius: '5px',\n    },\n    scheduleHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '8px',\n    },\n    scheduleTitle: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n    scheduleTime: {\n      color: '#007bff',\n      fontSize: '0.9rem',\n    },\n    scheduleDetails: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    homeworkItem: {\n      padding: '15px',\n      border: '1px solid #eee',\n      borderRadius: '8px',\n      marginBottom: '15px',\n    },\n    homeworkHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '10px',\n    },\n    homeworkTitle: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n    homeworkStatus: {\n      padding: '4px 12px',\n      borderRadius: '20px',\n      fontSize: '0.8rem',\n      fontWeight: 'bold',\n      color: 'white',\n    },\n    homeworkDetails: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    gradeItem: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n    },\n    gradeSubject: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n    gradeValue: {\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: '#007bff',\n    },\n    fullWidthCard: {\n      gridColumn: '1 / -1',\n    },\n    refreshButton: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      border: 'none',\n      padding: '8px 16px',\n      borderRadius: '5px',\n      cursor: 'pointer',\n      fontSize: '0.9rem',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '5px',\n      transition: 'background-color 0.3s ease',\n    },\n    headerActions: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '10px',\n    },\n    loadingContainer: {\n      textAlign: 'center',\n      padding: '60px 20px',\n      color: '#666',\n    },\n    errorContainer: {\n      textAlign: 'center',\n      padding: '40px 20px',\n      backgroundColor: '#f8d7da',\n      color: '#721c24',\n      borderRadius: '10px',\n      border: '1px solid #f5c6cb',\n      margin: '20px 0',\n    },\n    noDataContainer: {\n      textAlign: 'center',\n      padding: '40px',\n      backgroundColor: 'white',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      border: '2px dashed #dc3545',\n      gridColumn: '1 / -1',\n    },\n  };\n\n  // Générer les cartes de statistiques seulement si les données sont disponibles\n  const statCards = stats ? [\n    {\n      icon: FaChartLine,\n      number: stats.moyenne_generale ? `${stats.moyenne_generale}/20` : 'N/A',\n      label: 'Moyenne Générale',\n      color: stats.moyenne_generale >= 15 ? '#28a745' : stats.moyenne_generale >= 10 ? '#ffc107' : '#dc3545',\n      subtitle: `${stats.nombre_notes || 0} note(s)`\n    },\n    {\n      icon: FaClipboardList,\n      number: stats.devoirs_en_cours || 0,\n      label: 'Devoirs en Cours',\n      color: '#ffc107',\n      subtitle: 'À rendre prochainement'\n    },\n    {\n      icon: FaCalendarAlt,\n      number: stats.cours_aujourdhui || 0,\n      label: \"Cours Aujourd'hui\",\n      color: '#007bff',\n      subtitle: 'Emploi du temps'\n    },\n    {\n      icon: FaUserClock,\n      number: stats.total_absences || 0,\n      label: 'Total Absences',\n      color: '#dc3545',\n      subtitle: `${stats.absences_ce_mois || 0} ce mois`\n    },\n    {\n      icon: FaClock,\n      number: stats.total_retards || 0,\n      label: 'Total Retards',\n      color: '#fd7e14',\n      subtitle: `${stats.retards_ce_mois || 0} ce mois`\n    },\n    {\n      icon: FaFileAlt,\n      number: stats.quiz_completes || 0,\n      label: 'Quiz Complétés',\n      color: '#6f42c1',\n      subtitle: 'Évaluations terminées'\n    },\n  ] : [];\n\n  // Affichage de chargement\n  if (loading) {\n    return (\n      <div style={styles.container}>\n        <div style={styles.loadingContainer}>\n          <FaSync style={{ fontSize: '3rem', color: '#007bff', animation: 'spin 1s linear infinite' }} />\n          <h2>Chargement de vos données...</h2>\n          <p>Récupération des informations depuis la base de données</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête */}\n      <div style={styles.header}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n          <div>\n            <h1 style={styles.welcomeText}>\n              <FaGraduationCap style={{ marginRight: '15px', color: '#007bff' }} />\n              Bienvenue, {etudiantInfo ? `${etudiantInfo.prenom} ${etudiantInfo.nom}` : user?.email || 'Étudiant'}\n            </h1>\n            <p style={styles.subtitle}>\n              Tableau de bord étudiant - Suivez vos cours et vos résultats\n              {lastUpdate && ` • Dernière mise à jour: ${lastUpdate}`}\n              {etudiantInfo && ` • ${etudiantInfo.classe_nom || 'Classe non définie'} - ${etudiantInfo.filiere_nom || 'Filière non définie'}`}\n            </p>\n          </div>\n\n          <div style={styles.headerActions}>\n            <button\n              onClick={handleRefresh}\n              style={styles.refreshButton}\n              title=\"Actualiser les données maintenant\"\n            >\n              <FaSync /> Actualiser\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Affichage d'erreur */}\n      {error && (\n        <div style={styles.errorContainer}>\n          <FaExclamationTriangle style={{ fontSize: '2rem', marginBottom: '10px' }} />\n          <h3>Erreur de chargement</h3>\n          <p>{error}</p>\n          <button onClick={handleRefresh} style={styles.refreshButton}>\n            <FaSync /> Réessayer\n          </button>\n        </div>\n      )}\n\n      {/* Statistiques */}\n      <div style={styles.statsGrid}>\n        {statCards.map((stat, index) => (\n          <div \n            key={index} \n            style={styles.statCard}\n            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}\n          >\n            <div style={styles.statIcon}>\n              <stat.icon />\n            </div>\n            <div style={styles.statNumber}>{stat.number}</div>\n            <div style={styles.statLabel}>{stat.label}</div>\n          </div>\n        ))}\n      </div>\n\n      {/* Contenu principal */}\n      <div style={styles.contentGrid}>\n        {/* Emploi du temps */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaCalendarAlt style={styles.cardIcon} />\n            Emploi du Temps - Aujourd'hui\n          </h2>\n          {emploiDuTemps.map(cours => (\n            <div key={cours.id} style={styles.scheduleItem}>\n              <div style={styles.scheduleHeader}>\n                <span style={styles.scheduleTitle}>{cours.matiere}</span>\n                <span style={styles.scheduleTime}>{cours.heure}</span>\n              </div>\n              <div style={styles.scheduleDetails}>\n                Salle: {cours.salle} | Enseignant: {cours.enseignant}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Devoirs */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaClipboardList style={styles.cardIcon} />\n            Devoirs à Rendre\n          </h2>\n          {devoirs.map(devoir => (\n            <div key={devoir.id} style={styles.homeworkItem}>\n              <div style={styles.homeworkHeader}>\n                <span style={styles.homeworkTitle}>{devoir.titre}</span>\n                <span \n                  style={{\n                    ...styles.homeworkStatus, \n                    backgroundColor: getStatutColor(devoir.statut)\n                  }}\n                >\n                  {getStatutText(devoir.statut)}\n                </span>\n              </div>\n              <div style={styles.homeworkDetails}>\n                {devoir.matiere} | À rendre le: {devoir.dateRendu}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Notes récentes */}\n      <div style={{...styles.card, ...styles.fullWidthCard}}>\n        <h2 style={styles.cardTitle}>\n          <FaBookOpen style={styles.cardIcon} />\n          Notes Récentes\n        </h2>\n        {notes.map(note => (\n          <div key={note.id} style={styles.gradeItem}>\n            <div>\n              <span style={styles.gradeSubject}>{note.matiere}</span>\n              <div style={{ color: '#666', fontSize: '0.9rem' }}>\n                Coefficient: {note.coefficient} | Date: {note.date}\n              </div>\n            </div>\n            <span style={styles.gradeValue}>{note.note}/20</span>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default EtudiantDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACzK,OAAOC,KAAK,MAAM,OAAO;AAEzB,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAM;IAAEC;EAAK,CAAC,GAAGd,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdkC,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtEJ,kBAAkB,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;IAEnB,OAAO,MAAMK,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,IAAI,EAAChB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,EAAE,GAAE;QACb,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAMC,QAAQ,GAAG,MAAM7B,KAAK,CAAC8B,GAAG,CAC9B,kFAAkF5B,IAAI,CAACyB,EAAE,EAAE,EAC3F;QACEI,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,YAAY,EAAE;UAC1E,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,IAAIN,QAAQ,CAACK,IAAI,CAACzB,KAAK,IAAIoB,QAAQ,CAACK,IAAI,CAACE,QAAQ,EAAE;QAC1E;QACA1B,QAAQ,CAACmB,QAAQ,CAACK,IAAI,CAACzB,KAAK,CAAC;QAC7BG,eAAe,CAACiB,QAAQ,CAACK,IAAI,CAACE,QAAQ,CAAC;;QAEvC;QACA,IAAIP,QAAQ,CAACK,IAAI,CAACG,cAAc,EAAE;UAChC7B,QAAQ,CAACqB,QAAQ,CAACK,IAAI,CAACG,cAAc,CAAC;QACxC;QAEA,IAAIR,QAAQ,CAACK,IAAI,CAACI,gBAAgB,EAAE;UAClChC,UAAU,CAACuB,QAAQ,CAACK,IAAI,CAACI,gBAAgB,CAACC,GAAG,CAACC,MAAM,KAAK;YACvD,GAAGA,MAAM;YACTC,MAAM,EAAED,MAAM,CAACE,cAAc,IAAI,CAAC,GAAG,QAAQ,GACtCF,MAAM,CAACE,cAAc,IAAI,CAAC,GAAG,QAAQ,GAAG;UACjD,CAAC,CAAC,CAAC,CAAC;QACN;QAEA,IAAIb,QAAQ,CAACK,IAAI,CAACS,0BAA0B,EAAE;UAC5CvC,gBAAgB,CAACyB,QAAQ,CAACK,IAAI,CAACS,0BAA0B,CAACJ,GAAG,CAACK,KAAK,KAAK;YACtEjB,EAAE,EAAEkB,IAAI,CAACC,MAAM,CAAC,CAAC;YACjBC,OAAO,EAAEH,KAAK,CAACI,WAAW;YAC1BC,KAAK,EAAE,GAAGL,KAAK,CAACM,WAAW,IAAIN,KAAK,CAACO,SAAS,EAAE;YAChDC,KAAK,EAAER,KAAK,CAACQ,KAAK,IAAI,aAAa;YACnCC,UAAU,EAAET,KAAK,CAACU,cAAc,IAAI;UACtC,CAAC,CAAC,CAAC,CAAC;QACN;QAEA,IAAIzB,QAAQ,CAACK,IAAI,CAACqB,kBAAkB,EAAE;UACpCzC,oBAAoB,CAACe,QAAQ,CAACK,IAAI,CAACqB,kBAAkB,CAAC;QACxD;;QAEA;QACAnC,aAAa,CAAC,IAAIoC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAErDjC,OAAO,CAACC,GAAG,CAAC,sEAAsE,EAAEI,QAAQ,CAACK,IAAI,CAAC;MACpG,CAAC,MAAM;QACL,MAAM,IAAIN,KAAK,CAACC,QAAQ,CAACK,IAAI,CAACjB,KAAK,IAAI,gCAAgC,CAAC;MAC1E;IAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzEC,QAAQ,CAAC,gEAAgED,KAAK,CAACyC,OAAO,EAAE,CAAC;;MAEzF;MACAhD,QAAQ,CAAC,IAAI,CAAC;MACdE,eAAe,CAAC,IAAI,CAAC;MACrBJ,QAAQ,CAAC,EAAE,CAAC;MACZF,UAAU,CAAC,EAAE,CAAC;MACdF,gBAAgB,CAAC,EAAE,CAAC;MACpBU,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,aAAa,GAAGA,CAAA,KAAM;IAC1BnC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDJ,kBAAkB,CAAC,CAAC;EACtB,CAAC;EAED,MAAMuC,cAAc,GAAInB,MAAM,IAAK;IACjC,QAAOA,MAAM;MACX,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMoB,aAAa,GAAIpB,MAAM,IAAK;IAChC,QAAOA,MAAM;MACX,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMqB,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,YAAY,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE;MACXC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDK,QAAQ,EAAE;MACRD,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDI,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDU,QAAQ,EAAE;MACRb,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRb,QAAQ,EAAE,MAAM;MAChBF,YAAY,EAAE,MAAM;MACpBI,KAAK,EAAE;IACT,CAAC;IACDY,UAAU,EAAE;MACVd,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDiB,SAAS,EAAE;MACTb,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDgB,WAAW,EAAE;MACXX,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,SAAS;MAC9BC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDmB,IAAI,EAAE;MACJtB,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE;IACb,CAAC;IACDQ,SAAS,EAAE;MACTlB,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfc,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRC,WAAW,EAAE,MAAM;MACnBnB,KAAK,EAAE;IACT,CAAC;IACDoB,YAAY,EAAE;MACZ5B,OAAO,EAAE,MAAM;MACf6B,UAAU,EAAE,mBAAmB;MAC/B5B,eAAe,EAAE,SAAS;MAC1BG,YAAY,EAAE,MAAM;MACpBW,YAAY,EAAE;IAChB,CAAC;IACDe,cAAc,EAAE;MACdnB,OAAO,EAAE,MAAM;MACfoB,cAAc,EAAE,eAAe;MAC/BN,UAAU,EAAE,QAAQ;MACpBrB,YAAY,EAAE;IAChB,CAAC;IACD4B,aAAa,EAAE;MACbzB,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDyB,YAAY,EAAE;MACZzB,KAAK,EAAE,SAAS;MAChBF,QAAQ,EAAE;IACZ,CAAC;IACD4B,eAAe,EAAE;MACf1B,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACD6B,YAAY,EAAE;MACZnC,OAAO,EAAE,MAAM;MACfoC,MAAM,EAAE,gBAAgB;MACxBrB,YAAY,EAAE,KAAK;MACnBX,YAAY,EAAE;IAChB,CAAC;IACDiC,cAAc,EAAE;MACd1B,OAAO,EAAE,MAAM;MACfoB,cAAc,EAAE,eAAe;MAC/BN,UAAU,EAAE,QAAQ;MACpBrB,YAAY,EAAE;IAChB,CAAC;IACDkC,aAAa,EAAE;MACb/B,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACD+B,cAAc,EAAE;MACdvC,OAAO,EAAE,UAAU;MACnBe,YAAY,EAAE,MAAM;MACpBT,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDgC,eAAe,EAAE;MACfhC,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDmC,SAAS,EAAE;MACT9B,OAAO,EAAE,MAAM;MACfoB,cAAc,EAAE,eAAe;MAC/BN,UAAU,EAAE,QAAQ;MACpBzB,OAAO,EAAE,MAAM;MACf0C,YAAY,EAAE;IAChB,CAAC;IACDC,YAAY,EAAE;MACZpC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDoC,UAAU,EAAE;MACVtC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDqC,aAAa,EAAE;MACbC,UAAU,EAAE;IACd,CAAC;IACDC,aAAa,EAAE;MACb9C,eAAe,EAAE,SAAS;MAC1BO,KAAK,EAAE,OAAO;MACd4B,MAAM,EAAE,MAAM;MACdpC,OAAO,EAAE,UAAU;MACnBe,YAAY,EAAE,KAAK;MACnBiC,MAAM,EAAE,SAAS;MACjB1C,QAAQ,EAAE,QAAQ;MAClBK,OAAO,EAAE,MAAM;MACfc,UAAU,EAAE,QAAQ;MACpBZ,GAAG,EAAE,KAAK;MACVK,UAAU,EAAE;IACd,CAAC;IACD+B,aAAa,EAAE;MACbtC,OAAO,EAAE,MAAM;MACfc,UAAU,EAAE,QAAQ;MACpBZ,GAAG,EAAE;IACP,CAAC;IACDqC,gBAAgB,EAAE;MAChBjC,SAAS,EAAE,QAAQ;MACnBjB,OAAO,EAAE,WAAW;MACpBQ,KAAK,EAAE;IACT,CAAC;IACD2C,cAAc,EAAE;MACdlC,SAAS,EAAE,QAAQ;MACnBjB,OAAO,EAAE,WAAW;MACpBC,eAAe,EAAE,SAAS;MAC1BO,KAAK,EAAE,SAAS;MAChBO,YAAY,EAAE,MAAM;MACpBqB,MAAM,EAAE,mBAAmB;MAC3BgB,MAAM,EAAE;IACV,CAAC;IACDC,eAAe,EAAE;MACfpC,SAAS,EAAE,QAAQ;MACnBjB,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,OAAO;MACxBc,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCoB,MAAM,EAAE,oBAAoB;MAC5BU,UAAU,EAAE;IACd;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAG7G,KAAK,GAAG,CACxB;IACE8G,IAAI,EAAE9H,WAAW;IACjB+H,MAAM,EAAE/G,KAAK,CAACgH,gBAAgB,GAAG,GAAGhH,KAAK,CAACgH,gBAAgB,KAAK,GAAG,KAAK;IACvEC,KAAK,EAAE,kBAAkB;IACzBlD,KAAK,EAAE/D,KAAK,CAACgH,gBAAgB,IAAI,EAAE,GAAG,SAAS,GAAGhH,KAAK,CAACgH,gBAAgB,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;IACtGhD,QAAQ,EAAE,GAAGhE,KAAK,CAACkH,YAAY,IAAI,CAAC;EACtC,CAAC,EACD;IACEJ,IAAI,EAAE/H,eAAe;IACrBgI,MAAM,EAAE/G,KAAK,CAAC6B,gBAAgB,IAAI,CAAC;IACnCoF,KAAK,EAAE,kBAAkB;IACzBlD,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE8C,IAAI,EAAEhI,aAAa;IACnBiI,MAAM,EAAE/G,KAAK,CAACmH,gBAAgB,IAAI,CAAC;IACnCF,KAAK,EAAE,mBAAmB;IAC1BlD,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE8C,IAAI,EAAEzH,WAAW;IACjB0H,MAAM,EAAE/G,KAAK,CAACoH,cAAc,IAAI,CAAC;IACjCH,KAAK,EAAE,gBAAgB;IACvBlD,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,GAAGhE,KAAK,CAACqH,gBAAgB,IAAI,CAAC;EAC1C,CAAC,EACD;IACEP,IAAI,EAAE5H,OAAO;IACb6H,MAAM,EAAE/G,KAAK,CAACsH,aAAa,IAAI,CAAC;IAChCL,KAAK,EAAE,eAAe;IACtBlD,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,GAAGhE,KAAK,CAACuH,eAAe,IAAI,CAAC;EACzC,CAAC,EACD;IACET,IAAI,EAAExH,SAAS;IACfyH,MAAM,EAAE/G,KAAK,CAACwH,cAAc,IAAI,CAAC;IACjCP,KAAK,EAAE,gBAAgB;IACvBlD,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC,CACF,GAAG,EAAE;;EAEN;EACA,IAAI1D,OAAO,EAAE;IACX,oBACE9B,KAAA,CAAAiJ,aAAA;MAAKC,KAAK,EAAErE,MAAM,CAACC,SAAU;MAAAqE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3BxJ,KAAA,CAAAiJ,aAAA;MAAKC,KAAK,EAAErE,MAAM,CAACoD,gBAAiB;MAAAkB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAClCxJ,KAAA,CAAAiJ,aAAA,CAACrI,MAAM;MAACsI,KAAK,EAAE;QAAE7D,QAAQ,EAAE,MAAM;QAAEE,KAAK,EAAE,SAAS;QAAEkE,SAAS,EAAE;MAA0B,CAAE;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eAC/FxJ,KAAA,CAAAiJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iCAAgC,CAAC,eACrCxJ,KAAA,CAAAiJ,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,kEAA0D,CAC1D,CACF,CAAC;EAEV;EAEA,oBACExJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACC,SAAU;IAAAqE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACK,MAAO;IAAAiE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAE;MAAExD,OAAO,EAAE,MAAM;MAAEoB,cAAc,EAAE,eAAe;MAAEN,UAAU,EAAE;IAAa,CAAE;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzFxJ,KAAA,CAAAiJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExJ,KAAA,CAAAiJ,aAAA;IAAIC,KAAK,EAAErE,MAAM,CAACO,WAAY;IAAA+D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BxJ,KAAA,CAAAiJ,aAAA,CAAC5I,eAAe;IAAC6I,KAAK,EAAE;MAAExC,WAAW,EAAE,MAAM;MAAEnB,KAAK,EAAE;IAAU,CAAE;IAAA4D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC1D,EAAC9H,YAAY,GAAG,GAAGA,YAAY,CAACgI,MAAM,IAAIhI,YAAY,CAACiI,GAAG,EAAE,GAAG,CAAA1I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2I,KAAK,KAAI,UACvF,CAAC,eACL5J,KAAA,CAAAiJ,aAAA;IAAGC,KAAK,EAAErE,MAAM,CAACW,QAAS;IAAA2D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oEAEzB,EAACtH,UAAU,IAAI,4BAA4BA,UAAU,EAAE,EACtDR,YAAY,IAAI,MAAMA,YAAY,CAACmI,UAAU,IAAI,oBAAoB,MAAMnI,YAAY,CAACoI,WAAW,IAAI,qBAAqB,EAC5H,CACA,CAAC,eAEN9J,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACmD,aAAc;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BxJ,KAAA,CAAAiJ,aAAA;IACEc,OAAO,EAAErF,aAAc;IACvBwE,KAAK,EAAErE,MAAM,CAACiD,aAAc;IAC5BkC,KAAK,EAAC,sCAAmC;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzCxJ,KAAA,CAAAiJ,aAAA,CAACrI,MAAM;IAAAuI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACJ,CACL,CACF,CACF,CAAC,EAGLxH,KAAK,iBACJhC,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACqD,cAAe;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCxJ,KAAA,CAAAiJ,aAAA,CAACtI,qBAAqB;IAACuI,KAAK,EAAE;MAAE7D,QAAQ,EAAE,MAAM;MAAEF,YAAY,EAAE;IAAO,CAAE;IAAAgE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5ExJ,KAAA,CAAAiJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAwB,CAAC,eAC7BxJ,KAAA,CAAAiJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAIxH,KAAS,CAAC,eACdhC,KAAA,CAAAiJ,aAAA;IAAQc,OAAO,EAAErF,aAAc;IAACwE,KAAK,EAAErE,MAAM,CAACiD,aAAc;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1DxJ,KAAA,CAAAiJ,aAAA,CAACrI,MAAM;IAAAuI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,iBACJ,CACL,CACN,eAGDxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACY,SAAU;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BnB,SAAS,CAAC/E,GAAG,CAAC,CAAC2G,IAAI,EAAEC,KAAK,kBACzBlK,KAAA,CAAAiJ,aAAA;IACEkB,GAAG,EAAED,KAAM;IACXhB,KAAK,EAAErE,MAAM,CAACgB,QAAS;IACvBuE,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACpB,KAAK,CAACqB,SAAS,GAAG,kBAAmB;IACnEC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACpB,KAAK,CAACqB,SAAS,GAAG,eAAgB;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhExJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACqB,QAAS;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxJ,KAAA,CAAAiJ,aAAA,CAACgB,IAAI,CAAC3B,IAAI;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACNxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACsB,UAAW;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAES,IAAI,CAAC1B,MAAY,CAAC,eAClDvI,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACuB,SAAU;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAES,IAAI,CAACxB,KAAW,CAC5C,CACN,CACE,CAAC,eAGNzI,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACwB,WAAY;IAAA8C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7BxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACyB,IAAK;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBxJ,KAAA,CAAAiJ,aAAA;IAAIC,KAAK,EAAErE,MAAM,CAAC0B,SAAU;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxJ,KAAA,CAAAiJ,aAAA,CAAC3I,aAAa;IAAC4I,KAAK,EAAErE,MAAM,CAAC4B,QAAS;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,iCAEvC,CAAC,EACJtI,aAAa,CAACoC,GAAG,CAACK,KAAK,iBACtB3D,KAAA,CAAAiJ,aAAA;IAAKkB,GAAG,EAAExG,KAAK,CAACjB,EAAG;IAACwG,KAAK,EAAErE,MAAM,CAAC8B,YAAa;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7CxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACgC,cAAe;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCxJ,KAAA,CAAAiJ,aAAA;IAAMC,KAAK,EAAErE,MAAM,CAACkC,aAAc;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7F,KAAK,CAACG,OAAc,CAAC,eACzD9D,KAAA,CAAAiJ,aAAA;IAAMC,KAAK,EAAErE,MAAM,CAACmC,YAAa;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7F,KAAK,CAACK,KAAY,CAClD,CAAC,eACNhE,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACoC,eAAgB;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAC3B,EAAC7F,KAAK,CAACQ,KAAK,EAAC,iBAAe,EAACR,KAAK,CAACS,UACvC,CACF,CACN,CACE,CAAC,eAGNpE,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACyB,IAAK;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBxJ,KAAA,CAAAiJ,aAAA;IAAIC,KAAK,EAAErE,MAAM,CAAC0B,SAAU;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxJ,KAAA,CAAAiJ,aAAA,CAAC1I,eAAe;IAAC2I,KAAK,EAAErE,MAAM,CAAC4B,QAAS;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,uBAEzC,CAAC,EACJpI,OAAO,CAACkC,GAAG,CAACC,MAAM,iBACjBvD,KAAA,CAAAiJ,aAAA;IAAKkB,GAAG,EAAE5G,MAAM,CAACb,EAAG;IAACwG,KAAK,EAAErE,MAAM,CAACqC,YAAa;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9CxJ,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAACuC,cAAe;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCxJ,KAAA,CAAAiJ,aAAA;IAAMC,KAAK,EAAErE,MAAM,CAACwC,aAAc;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEjG,MAAM,CAACkH,KAAY,CAAC,eACxDzK,KAAA,CAAAiJ,aAAA;IACEC,KAAK,EAAE;MACL,GAAGrE,MAAM,CAACyC,cAAc;MACxBtC,eAAe,EAAEL,cAAc,CAACpB,MAAM,CAACC,MAAM;IAC/C,CAAE;IAAA2F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAED5E,aAAa,CAACrB,MAAM,CAACC,MAAM,CACxB,CACH,CAAC,eACNxD,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAErE,MAAM,CAAC0C,eAAgB;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCjG,MAAM,CAACO,OAAO,EAAC,qBAAgB,EAACP,MAAM,CAACmH,SACrC,CACF,CACN,CACE,CACF,CAAC,eAGN1K,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAGrE,MAAM,CAACyB,IAAI;MAAE,GAAGzB,MAAM,CAAC+C;IAAa,CAAE;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpDxJ,KAAA,CAAAiJ,aAAA;IAAIC,KAAK,EAAErE,MAAM,CAAC0B,SAAU;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxJ,KAAA,CAAAiJ,aAAA,CAACxI,UAAU;IAACyI,KAAK,EAAErE,MAAM,CAAC4B,QAAS;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,qBAEpC,CAAC,EACJlI,KAAK,CAACgC,GAAG,CAACqH,IAAI,iBACb3K,KAAA,CAAAiJ,aAAA;IAAKkB,GAAG,EAAEQ,IAAI,CAACjI,EAAG;IAACwG,KAAK,EAAErE,MAAM,CAAC2C,SAAU;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCxJ,KAAA,CAAAiJ,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExJ,KAAA,CAAAiJ,aAAA;IAAMC,KAAK,EAAErE,MAAM,CAAC6C,YAAa;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEmB,IAAI,CAAC7G,OAAc,CAAC,eACvD9D,KAAA,CAAAiJ,aAAA;IAAKC,KAAK,EAAE;MAAE3D,KAAK,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAS,CAAE;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eACpC,EAACmB,IAAI,CAACC,WAAW,EAAC,WAAS,EAACD,IAAI,CAACE,IAC3C,CACF,CAAC,eACN7K,KAAA,CAAAiJ,aAAA;IAAMC,KAAK,EAAErE,MAAM,CAAC8C,UAAW;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEmB,IAAI,CAACA,IAAI,EAAC,KAAS,CACjD,CACN,CACE,CACF,CAAC;AAEV,CAAC;AAED,eAAe3J,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}