{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\dashboards\\\\ResponsableDashboard.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap, FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle, FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck, FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar, FaCertificate, FaClipboardList, FaQuestionCircle } from 'react-icons/fa';\nconst ResponsableDashboard = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [stats, setStats] = useState(null);\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Rafraîchissement automatique toutes les 5 minutes si activé\n    let interval;\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        console.log('🔄 Rafraîchissement automatique des données...');\n        fetchDashboardData();\n      }, 5 * 60 * 1000); // 5 minutes\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [autoRefresh]);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🔄 Chargement des statistiques du dashboard...');\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('📊 Réponse API stats:', response.data);\n      if (response.data.success && response.data.stats) {\n        // Définir les statistiques directement depuis la base de données\n        setStats(response.data.stats);\n\n        // Traiter les activités récentes dynamiquement\n        if (response.data.stats.activites_recentes) {\n          setRecentActivities(response.data.stats.activites_recentes);\n        }\n\n        // Générer des alertes basées sur les données réelles de la base\n        const newAlertes = [];\n        if (response.data.stats.factures_impayees > 0) {\n          newAlertes.push({\n            id: 1,\n            type: 'warning',\n            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,\n            priority: 'high'\n          });\n        }\n        if (response.data.stats.absences_aujourdhui > 10) {\n          newAlertes.push({\n            id: 2,\n            type: 'urgent',\n            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,\n            priority: 'high'\n          });\n        }\n        if (response.data.stats.retards_aujourdhui > 5) {\n          newAlertes.push({\n            id: 3,\n            type: 'info',\n            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,\n            priority: 'medium'\n          });\n        }\n\n        // Alerte de système opérationnel seulement si les données sont chargées\n        newAlertes.push({\n          id: 4,\n          type: 'success',\n          message: `Système opérationnel - Dernière mise à jour: ${response.data.timestamp}`,\n          priority: 'low'\n        });\n        setAlertes(newAlertes);\n\n        // Mettre à jour l'heure de dernière mise à jour\n        setLastUpdate(new Date().toLocaleTimeString('fr-FR'));\n        console.log('✅ Statistiques chargées dynamiquement depuis la base de données:', response.data.stats);\n      } else {\n        throw new Error(response.data.error || 'Aucune donnée reçue du serveur');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données:', error);\n      setError(`Impossible de charger les statistiques depuis la base de données: ${error.message}`);\n\n      // Ne pas définir de données de fallback - garder stats à null\n      // pour forcer l'utilisateur à résoudre le problème de connexion\n      setStats(null);\n      setRecentActivities([]);\n      setAlertes([{\n        id: 1,\n        type: 'urgent',\n        message: 'Erreur de connexion à la base de données - Aucune donnée disponible',\n        priority: 'high'\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'inscription':\n        return '👤';\n      case 'note':\n        return '📝';\n      case 'absence':\n        return '⚠️';\n      case 'enseignant':\n        return '👨‍🏫';\n      default:\n        return '📢';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return '#dc3545';\n      case 'medium':\n        return '#ffc107';\n      case 'low':\n        return '#28a745';\n      default:\n        return '#6c757d';\n    }\n  };\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    header: {\n      marginBottom: '30px'\n    },\n    headerContent: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '15px'\n    },\n    headerActions: {\n      display: 'flex',\n      gap: '10px'\n    },\n    refreshButton: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      border: 'none',\n      padding: '10px 20px',\n      borderRadius: '8px',\n      cursor: 'pointer',\n      fontSize: '14px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      transition: 'background-color 0.3s ease'\n    },\n    errorBanner: {\n      backgroundColor: '#f8d7da',\n      color: '#721c24',\n      padding: '12px 20px',\n      borderRadius: '8px',\n      border: '1px solid #f5c6cb',\n      display: 'flex',\n      alignItems: 'center',\n      marginTop: '15px'\n    },\n    loadingContainer: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '400px',\n      textAlign: 'center'\n    },\n    loadingIcon: {\n      fontSize: '3rem',\n      color: '#007bff',\n      animation: 'spin 2s linear infinite',\n      marginBottom: '20px'\n    },\n    sectionHeader: {\n      marginBottom: '20px'\n    },\n    sectionTitle: {\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: '#333',\n      display: 'flex',\n      alignItems: 'center',\n      marginBottom: '10px'\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem'\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n      gap: '20px',\n      marginBottom: '40px'\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      border: '1px solid #e9ecef'\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '12px',\n      color: 'white'\n    },\n    statContent: {\n      flex: 1\n    },\n    statNumber: {\n      fontSize: '2.2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px'\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '1rem',\n      fontWeight: '500',\n      marginBottom: '3px'\n    },\n    statDescription: {\n      color: '#999',\n      fontSize: '0.85rem',\n      fontStyle: 'italic'\n    },\n    secondaryStatsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '15px',\n      marginBottom: '40px'\n    },\n    secondaryStatCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 8px rgba(0,0,0,0.08)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      border: '1px solid #f1f3f4'\n    },\n    secondaryStatIcon: {\n      fontSize: '1.8rem',\n      marginRight: '15px'\n    },\n    secondaryStatContent: {\n      flex: 1\n    },\n    secondaryStatNumber: {\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '3px'\n    },\n    secondaryStatLabel: {\n      color: '#666',\n      fontSize: '0.85rem',\n      fontWeight: '500'\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px'\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px'\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem'\n    },\n    activityContent: {\n      flex: 1\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px'\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem'\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid'\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333'\n    }\n  };\n\n  // Générer les cartes de statistiques seulement si les données sont disponibles\n  const statCards = stats ? [{\n    icon: FaBook,\n    number: stats.cours || 0,\n    label: 'Cours',\n    color: '#007bff',\n    link: '/cours',\n    description: 'Total des cours disponibles'\n  }, {\n    icon: FaBookOpen,\n    number: stats.matieres || 0,\n    label: 'Matières',\n    color: '#28a745',\n    link: '/matieres',\n    description: 'Matières enseignées'\n  }, {\n    icon: FaLayerGroup,\n    number: stats.groupes || 0,\n    label: 'Groupes',\n    color: '#17a2b8',\n    link: '/groupes',\n    description: 'Groupes d\\'étudiants'\n  }, {\n    icon: FaGraduationCap,\n    number: stats.etudiants || 0,\n    label: 'Étudiants',\n    color: '#6f42c1',\n    link: '/etudiants',\n    description: 'Étudiants inscrits'\n  }, {\n    icon: FaUserFriends,\n    number: stats.parents || 0,\n    label: 'Parents',\n    color: '#fd7e14',\n    link: '/parents',\n    description: 'Parents d\\'étudiants'\n  }, {\n    icon: FaStream,\n    number: stats.filieres || 0,\n    label: 'Filières',\n    color: '#e83e8c',\n    link: '/filieres',\n    description: 'Filières d\\'études'\n  }, {\n    icon: FaIdCard,\n    number: stats.utilisateurs || 0,\n    label: 'Utilisateurs',\n    color: '#20c997',\n    link: '/utilisateurs',\n    description: 'Utilisateurs du système'\n  }, {\n    icon: FaChalkboardTeacher,\n    number: stats.enseignants || 0,\n    label: 'Enseignants',\n    color: '#ffc107',\n    link: '/enseignants',\n    description: 'Professeurs actifs'\n  }] : [];\n\n  // Statistiques secondaires pour le monitoring - seulement si les données sont disponibles\n  const secondaryStats = stats ? [{\n    icon: FaUsers,\n    number: stats.classes || 0,\n    label: 'Classes',\n    color: '#6c757d',\n    link: '/classes'\n  }, {\n    icon: FaExclamationTriangle,\n    number: stats.absences_aujourdhui || 0,\n    label: \"Absences Aujourd'hui\",\n    color: '#dc3545',\n    link: '/absences'\n  }, {\n    icon: FaClock,\n    number: stats.retards_aujourdhui || 0,\n    label: \"Retards Aujourd'hui\",\n    color: '#fd7e14',\n    link: '/retards'\n  }, {\n    icon: FaClipboardList,\n    number: stats.devoirs_en_cours || 0,\n    label: \"Devoirs en Cours\",\n    color: '#17a2b8',\n    link: '/devoirs'\n  }, {\n    icon: FaQuestionCircle,\n    number: stats.quiz_actifs || 0,\n    label: \"Quiz Actifs\",\n    color: '#6f42c1',\n    link: '/quiz'\n  }, {\n    icon: FaFileInvoiceDollar,\n    number: stats.factures_impayees || 0,\n    label: \"Factures Impayées\",\n    color: '#dc3545',\n    link: '/factures'\n  }, {\n    icon: FaCertificate,\n    number: stats.diplomes_annee || 0,\n    label: \"Diplômes Cette Année\",\n    color: '#28a745',\n    link: '/diplomes'\n  }] : [];\n  const quickActions = [{\n    icon: FaUsers,\n    title: 'Gérer les Utilisateurs',\n    description: 'Ajouter, modifier ou supprimer des utilisateurs',\n    link: '/utilisateurs'\n  }, {\n    icon: FaChalkboardTeacher,\n    title: 'Gérer les Cours',\n    description: 'Planifier et organiser les cours',\n    link: '/cours'\n  }, {\n    icon: FaGraduationCap,\n    title: 'Gérer les Classes',\n    description: 'Créer et organiser les classes',\n    link: '/classes'\n  }, {\n    icon: FaCog,\n    title: 'Configuration',\n    description: 'Paramètres système et configuration',\n    link: '/roles'\n  }, {\n    icon: FaFileInvoiceDollar,\n    title: 'Gestion Financière',\n    description: 'Factures et paiements',\n    link: '/factures'\n  }, {\n    icon: FaCertificate,\n    title: 'Diplômes',\n    description: 'Gestion des diplômes',\n    link: '/diplomes'\n  }];\n  const handleRefresh = () => {\n    fetchDashboardData();\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.container,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.loadingContainer,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(FaSync, {\n      style: styles.loadingIcon,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 11\n      }\n    }), /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }\n    }, \"Chargement des statistiques...\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 11\n      }\n    }, \"Veuillez patienter pendant que nous r\\xE9cup\\xE9rons les donn\\xE9es.\")));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.headerContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.welcomeText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaUserTie, {\n    style: {\n      marginRight: '15px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 15\n    }\n  }), \"Bienvenue, \", (user === null || user === void 0 ? void 0 : user.email) || 'Responsable'), /*#__PURE__*/React.createElement(\"p\", {\n    style: styles.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 13\n    }\n  }, \"Tableau de bord administrateur - Vue d'ensemble de l'\\xE9cole\", lastUpdate && ` • Dernière mise à jour: ${lastUpdate}`, stats && ` • ${Object.keys(stats).length} statistiques chargées`)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.headerActions,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setAutoRefresh(!autoRefresh),\n    style: {\n      ...styles.refreshButton,\n      backgroundColor: autoRefresh ? '#28a745' : '#6c757d',\n      marginRight: '10px'\n    },\n    title: autoRefresh ? 'Désactiver le rafraîchissement automatique' : 'Activer le rafraîchissement automatique',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 13\n    }\n  }, autoRefresh ? '🔄 Auto ON' : '⏸️ Auto OFF'), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleRefresh,\n    style: styles.refreshButton,\n    title: \"Actualiser les donn\\xE9es maintenant\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaSync, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 15\n    }\n  }), \" Actualiser\"))), error && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.errorBanner,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaTimes, {\n    style: {\n      marginRight: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 13\n    }\n  }), error)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.sectionHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.sectionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaChartBar, {\n    style: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 11\n    }\n  }), \"Statistiques Principales\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 7\n    }\n  }, stats ? statCards.map((stat, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: stat.link || '#',\n    style: {\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.statIcon,\n      backgroundColor: stat.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 19\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 19\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 19\n    }\n  }, stat.label), stat.description && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statDescription,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 21\n    }\n  }, stat.description))))) : /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      gridColumn: '1 / -1',\n      textAlign: 'center',\n      padding: '40px',\n      backgroundColor: 'white',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      border: '2px dashed #dc3545'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    style: {\n      fontSize: '3rem',\n      color: '#dc3545',\n      marginBottom: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#dc3545',\n      marginBottom: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 13\n    }\n  }, \"Aucune donn\\xE9e disponible\"), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      color: '#666',\n      marginBottom: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 13\n    }\n  }, \"Impossible de charger les statistiques depuis la base de donn\\xE9es.\", /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 15\n    }\n  }), \"V\\xE9rifiez la connexion au serveur et actualisez la page.\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleRefresh,\n    style: {\n      ...styles.refreshButton,\n      backgroundColor: '#dc3545',\n      fontSize: '16px',\n      padding: '12px 24px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaSync, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 15\n    }\n  }), \" R\\xE9essayer\"))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.sectionHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.sectionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaCalendarAlt, {\n    style: {\n      marginRight: '10px',\n      color: '#28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 11\n    }\n  }), \"Monitoring & Activit\\xE9\")), stats && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 9\n    }\n  }, secondaryStats.map((stat, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: stat.link || '#',\n    style: {\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-3px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.secondaryStatIcon,\n      color: stat.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 19\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 674,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 19\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.secondaryStatLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 19\n    }\n  }, stat.label)))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.quickActionsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 7\n    }\n  }, quickActions.map((action, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: action.link,\n    style: styles.actionCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(action.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 13\n    }\n  }, action.title), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionDescription,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 13\n    }\n  }, action.description)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.contentGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaChartBar, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 13\n    }\n  }), \"Activit\\xE9s R\\xE9centes\"), recentActivities.map(activity => /*#__PURE__*/React.createElement(\"div\", {\n    key: activity.id,\n    style: styles.activityItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.activityIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 15\n    }\n  }, getActivityIcon(activity.type)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 17\n    }\n  }, activity.message), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityTime,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 17\n    }\n  }, activity.time))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 13\n    }\n  }), \"Alertes & Notifications\"), alertes.map(alerte => /*#__PURE__*/React.createElement(\"div\", {\n    key: alerte.id,\n    style: {\n      ...styles.alertItem,\n      borderLeftColor: getPriorityColor(alerte.priority),\n      backgroundColor: `${getPriorityColor(alerte.priority)}10`\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.alertMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 15\n    }\n  }, alerte.message))))));\n};\nexport default ResponsableDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "Link", "axios", "FaUserTie", "FaUsers", "FaChalkboardTeacher", "FaGraduationCap", "FaUserFriends", "FaChartBar", "FaCog", "FaCalendarAlt", "FaExclamationTriangle", "FaBook", "FaBookOpen", "FaLayerGroup", "FaUserGraduate", "FaUserCheck", "FaStream", "FaIdCard", "FaSync", "FaTimes", "FaClock", "FaFileInvoiceDollar", "FaCertificate", "FaClipboardList", "FaQuestionCircle", "ResponsableDashboard", "user", "stats", "setStats", "recentActivities", "setRecentActivities", "alertes", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "lastUpdate", "setLastUpdate", "autoRefresh", "setAutoRefresh", "fetchDashboardData", "interval", "setInterval", "console", "log", "clearInterval", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "success", "activites_recentes", "newAlertes", "factures_impayees", "push", "id", "type", "message", "priority", "absences_aujourdhui", "retards_aujourdhui", "timestamp", "Date", "toLocaleTimeString", "Error", "getActivityIcon", "getPriorityColor", "styles", "container", "padding", "backgroundColor", "minHeight", "header", "marginBottom", "headerContent", "display", "justifyContent", "alignItems", "headerActions", "gap", "refreshButton", "color", "border", "borderRadius", "cursor", "fontSize", "transition", "errorBanner", "marginTop", "loadingContainer", "flexDirection", "textAlign", "loadingIcon", "animation", "section<PERSON><PERSON><PERSON>", "sectionTitle", "fontWeight", "welcomeText", "subtitle", "statsGrid", "gridTemplateColumns", "statCard", "boxShadow", "statIcon", "marginRight", "statContent", "flex", "statNumber", "statLabel", "statDescription", "fontStyle", "secondaryStatsGrid", "secondaryStatCard", "secondaryStatIcon", "secondaryStatContent", "secondaryStatNumber", "secondaryStatLabel", "quickActionsGrid", "actionCard", "textDecoration", "actionIcon", "actionTitle", "actionDescription", "contentGrid", "card", "cardTitle", "cardIcon", "activityItem", "borderBottom", "activityIcon", "activityContent", "activityMessage", "activityTime", "alertItem", "borderLeft", "alertMessage", "statCards", "icon", "number", "cours", "label", "link", "description", "matieres", "groupes", "etudiants", "parents", "filieres", "utilisateurs", "enseignants", "secondaryStats", "classes", "devoirs_en_cours", "quiz_actifs", "diplomes_annee", "quickActions", "title", "handleRefresh", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "Object", "keys", "length", "onClick", "map", "stat", "index", "key", "to", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "gridColumn", "action", "activity", "time", "alerte", "borderLeftColor"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/dashboards/ResponsableDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap,\n  FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle,\n  FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck,\n  FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar,\n  FaCertificate, FaClipboardList, FaQuestionCircle\n} from 'react-icons/fa';\n\nconst ResponsableDashboard = () => {\n  const { user } = useContext(AuthContext);\n  const [stats, setStats] = useState(null);\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdate, setLastUpdate] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n\n    // Rafraîchissement automatique toutes les 5 minutes si activé\n    let interval;\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        console.log('🔄 Rafraîchissement automatique des données...');\n        fetchDashboardData();\n      }, 5 * 60 * 1000); // 5 minutes\n    }\n\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [autoRefresh]);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🔄 Chargement des statistiques du dashboard...');\n\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      console.log('📊 Réponse API stats:', response.data);\n\n      if (response.data.success && response.data.stats) {\n        // Définir les statistiques directement depuis la base de données\n        setStats(response.data.stats);\n\n        // Traiter les activités récentes dynamiquement\n        if (response.data.stats.activites_recentes) {\n          setRecentActivities(response.data.stats.activites_recentes);\n        }\n\n        // Générer des alertes basées sur les données réelles de la base\n        const newAlertes = [];\n\n        if (response.data.stats.factures_impayees > 0) {\n          newAlertes.push({\n            id: 1,\n            type: 'warning',\n            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,\n            priority: 'high'\n          });\n        }\n\n        if (response.data.stats.absences_aujourdhui > 10) {\n          newAlertes.push({\n            id: 2,\n            type: 'urgent',\n            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,\n            priority: 'high'\n          });\n        }\n\n        if (response.data.stats.retards_aujourdhui > 5) {\n          newAlertes.push({\n            id: 3,\n            type: 'info',\n            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,\n            priority: 'medium'\n          });\n        }\n\n        // Alerte de système opérationnel seulement si les données sont chargées\n        newAlertes.push({\n          id: 4,\n          type: 'success',\n          message: `Système opérationnel - Dernière mise à jour: ${response.data.timestamp}`,\n          priority: 'low'\n        });\n\n        setAlertes(newAlertes);\n\n        // Mettre à jour l'heure de dernière mise à jour\n        setLastUpdate(new Date().toLocaleTimeString('fr-FR'));\n\n        console.log('✅ Statistiques chargées dynamiquement depuis la base de données:', response.data.stats);\n      } else {\n        throw new Error(response.data.error || 'Aucune donnée reçue du serveur');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données:', error);\n      setError(`Impossible de charger les statistiques depuis la base de données: ${error.message}`);\n\n      // Ne pas définir de données de fallback - garder stats à null\n      // pour forcer l'utilisateur à résoudre le problème de connexion\n      setStats(null);\n      setRecentActivities([]);\n      setAlertes([{\n        id: 1,\n        type: 'urgent',\n        message: 'Erreur de connexion à la base de données - Aucune donnée disponible',\n        priority: 'high'\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getActivityIcon = (type) => {\n    switch(type) {\n      case 'inscription': return '👤';\n      case 'note': return '📝';\n      case 'absence': return '⚠️';\n      case 'enseignant': return '👨‍🏫';\n      default: return '📢';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch(priority) {\n      case 'high': return '#dc3545';\n      case 'medium': return '#ffc107';\n      case 'low': return '#28a745';\n      default: return '#6c757d';\n    }\n  };\n\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh',\n    },\n    header: {\n      marginBottom: '30px',\n    },\n    headerContent: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '15px',\n    },\n    headerActions: {\n      display: 'flex',\n      gap: '10px',\n    },\n    refreshButton: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      border: 'none',\n      padding: '10px 20px',\n      borderRadius: '8px',\n      cursor: 'pointer',\n      fontSize: '14px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      transition: 'background-color 0.3s ease',\n    },\n    errorBanner: {\n      backgroundColor: '#f8d7da',\n      color: '#721c24',\n      padding: '12px 20px',\n      borderRadius: '8px',\n      border: '1px solid #f5c6cb',\n      display: 'flex',\n      alignItems: 'center',\n      marginTop: '15px',\n    },\n    loadingContainer: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '400px',\n      textAlign: 'center',\n    },\n    loadingIcon: {\n      fontSize: '3rem',\n      color: '#007bff',\n      animation: 'spin 2s linear infinite',\n      marginBottom: '20px',\n    },\n    sectionHeader: {\n      marginBottom: '20px',\n    },\n    sectionTitle: {\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: '#333',\n      display: 'flex',\n      alignItems: 'center',\n      marginBottom: '10px',\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem',\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n      gap: '20px',\n      marginBottom: '40px',\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '12px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      border: '1px solid #e9ecef',\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '12px',\n      color: 'white',\n    },\n    statContent: {\n      flex: 1,\n    },\n    statNumber: {\n      fontSize: '2.2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px',\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '1rem',\n      fontWeight: '500',\n      marginBottom: '3px',\n    },\n    statDescription: {\n      color: '#999',\n      fontSize: '0.85rem',\n      fontStyle: 'italic',\n    },\n    secondaryStatsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '15px',\n      marginBottom: '40px',\n    },\n    secondaryStatCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 8px rgba(0,0,0,0.08)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      border: '1px solid #f1f3f4',\n    },\n    secondaryStatIcon: {\n      fontSize: '1.8rem',\n      marginRight: '15px',\n    },\n    secondaryStatContent: {\n      flex: 1,\n    },\n    secondaryStatNumber: {\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '3px',\n    },\n    secondaryStatLabel: {\n      color: '#666',\n      fontSize: '0.85rem',\n      fontWeight: '500',\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit',\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px',\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px',\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff',\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem',\n    },\n    activityContent: {\n      flex: 1,\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px',\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem',\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid',\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n  };\n\n  // Générer les cartes de statistiques seulement si les données sont disponibles\n  const statCards = stats ? [\n    {\n      icon: FaBook,\n      number: stats.cours || 0,\n      label: 'Cours',\n      color: '#007bff',\n      link: '/cours',\n      description: 'Total des cours disponibles'\n    },\n    {\n      icon: FaBookOpen,\n      number: stats.matieres || 0,\n      label: 'Matières',\n      color: '#28a745',\n      link: '/matieres',\n      description: 'Matières enseignées'\n    },\n    {\n      icon: FaLayerGroup,\n      number: stats.groupes || 0,\n      label: 'Groupes',\n      color: '#17a2b8',\n      link: '/groupes',\n      description: 'Groupes d\\'étudiants'\n    },\n    {\n      icon: FaGraduationCap,\n      number: stats.etudiants || 0,\n      label: 'Étudiants',\n      color: '#6f42c1',\n      link: '/etudiants',\n      description: 'Étudiants inscrits'\n    },\n    {\n      icon: FaUserFriends,\n      number: stats.parents || 0,\n      label: 'Parents',\n      color: '#fd7e14',\n      link: '/parents',\n      description: 'Parents d\\'étudiants'\n    },\n    {\n      icon: FaStream,\n      number: stats.filieres || 0,\n      label: 'Filières',\n      color: '#e83e8c',\n      link: '/filieres',\n      description: 'Filières d\\'études'\n    },\n    {\n      icon: FaIdCard,\n      number: stats.utilisateurs || 0,\n      label: 'Utilisateurs',\n      color: '#20c997',\n      link: '/utilisateurs',\n      description: 'Utilisateurs du système'\n    },\n    {\n      icon: FaChalkboardTeacher,\n      number: stats.enseignants || 0,\n      label: 'Enseignants',\n      color: '#ffc107',\n      link: '/enseignants',\n      description: 'Professeurs actifs'\n    },\n  ] : [];\n\n  // Statistiques secondaires pour le monitoring - seulement si les données sont disponibles\n  const secondaryStats = stats ? [\n    {\n      icon: FaUsers,\n      number: stats.classes || 0,\n      label: 'Classes',\n      color: '#6c757d',\n      link: '/classes'\n    },\n    {\n      icon: FaExclamationTriangle,\n      number: stats.absences_aujourdhui || 0,\n      label: \"Absences Aujourd'hui\",\n      color: '#dc3545',\n      link: '/absences'\n    },\n    {\n      icon: FaClock,\n      number: stats.retards_aujourdhui || 0,\n      label: \"Retards Aujourd'hui\",\n      color: '#fd7e14',\n      link: '/retards'\n    },\n    {\n      icon: FaClipboardList,\n      number: stats.devoirs_en_cours || 0,\n      label: \"Devoirs en Cours\",\n      color: '#17a2b8',\n      link: '/devoirs'\n    },\n    {\n      icon: FaQuestionCircle,\n      number: stats.quiz_actifs || 0,\n      label: \"Quiz Actifs\",\n      color: '#6f42c1',\n      link: '/quiz'\n    },\n    {\n      icon: FaFileInvoiceDollar,\n      number: stats.factures_impayees || 0,\n      label: \"Factures Impayées\",\n      color: '#dc3545',\n      link: '/factures'\n    },\n    {\n      icon: FaCertificate,\n      number: stats.diplomes_annee || 0,\n      label: \"Diplômes Cette Année\",\n      color: '#28a745',\n      link: '/diplomes'\n    },\n  ] : [];\n\n  const quickActions = [\n    { icon: FaUsers, title: 'Gérer les Utilisateurs', description: 'Ajouter, modifier ou supprimer des utilisateurs', link: '/utilisateurs' },\n    { icon: FaChalkboardTeacher, title: 'Gérer les Cours', description: 'Planifier et organiser les cours', link: '/cours' },\n    { icon: FaGraduationCap, title: 'Gérer les Classes', description: 'Créer et organiser les classes', link: '/classes' },\n    { icon: FaCog, title: 'Configuration', description: 'Paramètres système et configuration', link: '/roles' },\n    { icon: FaFileInvoiceDollar, title: 'Gestion Financière', description: 'Factures et paiements', link: '/factures' },\n    { icon: FaCertificate, title: 'Diplômes', description: 'Gestion des diplômes', link: '/diplomes' },\n  ];\n\n  const handleRefresh = () => {\n    fetchDashboardData();\n  };\n\n  if (loading) {\n    return (\n      <div style={styles.container}>\n        <div style={styles.loadingContainer}>\n          <FaSync style={styles.loadingIcon} />\n          <h2>Chargement des statistiques...</h2>\n          <p>Veuillez patienter pendant que nous récupérons les données.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête amélioré */}\n      <div style={styles.header}>\n        <div style={styles.headerContent}>\n          <div>\n            <h1 style={styles.welcomeText}>\n              <FaUserTie style={{ marginRight: '15px', color: '#007bff' }} />\n              Bienvenue, {user?.email || 'Responsable'}\n            </h1>\n            <p style={styles.subtitle}>\n              Tableau de bord administrateur - Vue d'ensemble de l'école\n              {lastUpdate && ` • Dernière mise à jour: ${lastUpdate}`}\n              {stats && ` • ${Object.keys(stats).length} statistiques chargées`}\n            </p>\n          </div>\n          <div style={styles.headerActions}>\n            <button\n              onClick={() => setAutoRefresh(!autoRefresh)}\n              style={{\n                ...styles.refreshButton,\n                backgroundColor: autoRefresh ? '#28a745' : '#6c757d',\n                marginRight: '10px'\n              }}\n              title={autoRefresh ? 'Désactiver le rafraîchissement automatique' : 'Activer le rafraîchissement automatique'}\n            >\n              {autoRefresh ? '🔄 Auto ON' : '⏸️ Auto OFF'}\n            </button>\n            <button\n              onClick={handleRefresh}\n              style={styles.refreshButton}\n              title=\"Actualiser les données maintenant\"\n            >\n              <FaSync /> Actualiser\n            </button>\n          </div>\n        </div>\n\n        {error && (\n          <div style={styles.errorBanner}>\n            <FaTimes style={{ marginRight: '10px' }} />\n            {error}\n          </div>\n        )}\n      </div>\n\n      {/* Statistiques principales */}\n      <div style={styles.sectionHeader}>\n        <h2 style={styles.sectionTitle}>\n          <FaChartBar style={{ marginRight: '10px', color: '#007bff' }} />\n          Statistiques Principales\n        </h2>\n      </div>\n\n      <div style={styles.statsGrid}>\n        {stats ? (\n          statCards.map((stat, index) => (\n            <Link\n              key={index}\n              to={stat.link || '#'}\n              style={{ textDecoration: 'none', color: 'inherit' }}\n            >\n              <div\n                style={styles.statCard}\n                onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n                onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n              >\n                <div style={{...styles.statIcon, backgroundColor: stat.color}}>\n                  <stat.icon />\n                </div>\n                <div style={styles.statContent}>\n                  <div style={styles.statNumber}>{stat.number}</div>\n                  <div style={styles.statLabel}>{stat.label}</div>\n                  {stat.description && (\n                    <div style={styles.statDescription}>{stat.description}</div>\n                  )}\n                </div>\n              </div>\n            </Link>\n          ))\n        ) : (\n          <div style={{\n            gridColumn: '1 / -1',\n            textAlign: 'center',\n            padding: '40px',\n            backgroundColor: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n            border: '2px dashed #dc3545'\n          }}>\n            <FaExclamationTriangle style={{ fontSize: '3rem', color: '#dc3545', marginBottom: '20px' }} />\n            <h3 style={{ color: '#dc3545', marginBottom: '10px' }}>Aucune donnée disponible</h3>\n            <p style={{ color: '#666', marginBottom: '20px' }}>\n              Impossible de charger les statistiques depuis la base de données.\n              <br />Vérifiez la connexion au serveur et actualisez la page.\n            </p>\n            <button\n              onClick={handleRefresh}\n              style={{\n                ...styles.refreshButton,\n                backgroundColor: '#dc3545',\n                fontSize: '16px',\n                padding: '12px 24px'\n              }}\n            >\n              <FaSync /> Réessayer\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Statistiques secondaires */}\n      <div style={styles.sectionHeader}>\n        <h2 style={styles.sectionTitle}>\n          <FaCalendarAlt style={{ marginRight: '10px', color: '#28a745' }} />\n          Monitoring & Activité\n        </h2>\n      </div>\n\n      {stats && (\n        <div style={styles.secondaryStatsGrid}>\n          {secondaryStats.map((stat, index) => (\n            <Link\n              key={index}\n              to={stat.link || '#'}\n              style={{ textDecoration: 'none', color: 'inherit' }}\n            >\n              <div\n                style={styles.secondaryStatCard}\n                onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-3px)'}\n                onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n              >\n                <div style={{...styles.secondaryStatIcon, color: stat.color}}>\n                  <stat.icon />\n                </div>\n                <div style={styles.secondaryStatContent}>\n                  <div style={styles.secondaryStatNumber}>{stat.number}</div>\n                  <div style={styles.secondaryStatLabel}>{stat.label}</div>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      )}\n\n      {/* Actions rapides */}\n      <div style={styles.quickActionsGrid}>\n        {quickActions.map((action, index) => (\n          <Link\n            key={index}\n            to={action.link}\n            style={styles.actionCard}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n          >\n            <div style={styles.actionIcon}>\n              <action.icon />\n            </div>\n            <div style={styles.actionTitle}>{action.title}</div>\n            <div style={styles.actionDescription}>{action.description}</div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Contenu principal */}\n      <div style={styles.contentGrid}>\n        {/* Activités récentes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaChartBar style={styles.cardIcon} />\n            Activités Récentes\n          </h2>\n          {recentActivities.map(activity => (\n            <div key={activity.id} style={styles.activityItem}>\n              <span style={styles.activityIcon}>\n                {getActivityIcon(activity.type)}\n              </span>\n              <div style={styles.activityContent}>\n                <div style={styles.activityMessage}>{activity.message}</div>\n                <div style={styles.activityTime}>{activity.time}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Alertes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaExclamationTriangle style={styles.cardIcon} />\n            Alertes & Notifications\n          </h2>\n          {alertes.map(alerte => (\n            <div \n              key={alerte.id} \n              style={{\n                ...styles.alertItem,\n                borderLeftColor: getPriorityColor(alerte.priority),\n                backgroundColor: `${getPriorityColor(alerte.priority)}10`\n              }}\n            >\n              <div style={styles.alertMessage}>{alerte.message}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResponsableDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,EACxDC,aAAa,EAAEC,UAAU,EAAEC,KAAK,EAAEC,aAAa,EAAEC,qBAAqB,EACtEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAC7DC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EACjEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAC3C,gBAAgB;AAEvB,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAG5B,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd4C,kBAAkB,CAAC,CAAC;;IAEpB;IACA,IAAIC,QAAQ;IACZ,IAAIH,WAAW,EAAE;MACfG,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7DJ,kBAAkB,CAAC,CAAC;MACtB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IACrB;IAEA,OAAO,MAAM;MACX,IAAIC,QAAQ,EAAEI,aAAa,CAACJ,QAAQ,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;EAEjB,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,GAAG,CAAC,gEAAgE,EAAE;QACjGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEFH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,QAAQ,CAACI,IAAI,CAAC;MAEnD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,IAAIL,QAAQ,CAACI,IAAI,CAAC3B,KAAK,EAAE;QAChD;QACAC,QAAQ,CAACsB,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAAC;;QAE7B;QACA,IAAIuB,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAAC6B,kBAAkB,EAAE;UAC1C1B,mBAAmB,CAACoB,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAAC6B,kBAAkB,CAAC;QAC7D;;QAEA;QACA,MAAMC,UAAU,GAAG,EAAE;QAErB,IAAIP,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAAC+B,iBAAiB,GAAG,CAAC,EAAE;UAC7CD,UAAU,CAACE,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,GAAGZ,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAAC+B,iBAAiB,oCAAoC;YACrFK,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAIb,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAACqC,mBAAmB,GAAG,EAAE,EAAE;UAChDP,UAAU,CAACE,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE,wCAAwCZ,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAACqC,mBAAmB,EAAE;YAC1FD,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAIb,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAACsC,kBAAkB,GAAG,CAAC,EAAE;UAC9CR,UAAU,CAACE,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,GAAGZ,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAACsC,kBAAkB,mCAAmC;YACrFF,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;;QAEA;QACAN,UAAU,CAACE,IAAI,CAAC;UACdC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,gDAAgDZ,QAAQ,CAACI,IAAI,CAACY,SAAS,EAAE;UAClFH,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF/B,UAAU,CAACyB,UAAU,CAAC;;QAEtB;QACAnB,aAAa,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAErDxB,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAEK,QAAQ,CAACI,IAAI,CAAC3B,KAAK,CAAC;MACtG,CAAC,MAAM;QACL,MAAM,IAAI0C,KAAK,CAACnB,QAAQ,CAACI,IAAI,CAACnB,KAAK,IAAI,gCAAgC,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEC,QAAQ,CAAC,qEAAqED,KAAK,CAAC2B,OAAO,EAAE,CAAC;;MAE9F;MACA;MACAlC,QAAQ,CAAC,IAAI,CAAC;MACdE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,UAAU,CAAC,CAAC;QACV4B,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,qEAAqE;QAC9EC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,eAAe,GAAIT,IAAI,IAAK;IAChC,QAAOA,IAAI;MACT,KAAK,aAAa;QAAE,OAAO,IAAI;MAC/B,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,SAAS;QAAE,OAAO,IAAI;MAC3B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMU,gBAAgB,GAAIR,QAAQ,IAAK;IACrC,QAAOA,QAAQ;MACb,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMS,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,YAAY,EAAE;IAChB,CAAC;IACDC,aAAa,EAAE;MACbC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpBJ,YAAY,EAAE;IAChB,CAAC;IACDK,aAAa,EAAE;MACbH,OAAO,EAAE,MAAM;MACfI,GAAG,EAAE;IACP,CAAC;IACDC,aAAa,EAAE;MACbV,eAAe,EAAE,SAAS;MAC1BW,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,MAAM;MACdb,OAAO,EAAE,WAAW;MACpBc,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,MAAM;MAChBV,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBE,GAAG,EAAE,KAAK;MACVO,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;MACXjB,eAAe,EAAE,SAAS;MAC1BW,KAAK,EAAE,SAAS;MAChBZ,OAAO,EAAE,WAAW;MACpBc,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE,mBAAmB;MAC3BP,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,SAAS,EAAE;IACb,CAAC;IACDC,gBAAgB,EAAE;MAChBd,OAAO,EAAE,MAAM;MACfe,aAAa,EAAE,QAAQ;MACvBb,UAAU,EAAE,QAAQ;MACpBD,cAAc,EAAE,QAAQ;MACxBL,SAAS,EAAE,OAAO;MAClBoB,SAAS,EAAE;IACb,CAAC;IACDC,WAAW,EAAE;MACXP,QAAQ,EAAE,MAAM;MAChBJ,KAAK,EAAE,SAAS;MAChBY,SAAS,EAAE,yBAAyB;MACpCpB,YAAY,EAAE;IAChB,CAAC;IACDqB,aAAa,EAAE;MACbrB,YAAY,EAAE;IAChB,CAAC;IACDsB,YAAY,EAAE;MACZV,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbN,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBJ,YAAY,EAAE;IAChB,CAAC;IACDwB,WAAW,EAAE;MACXZ,QAAQ,EAAE,MAAM;MAChBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACDyB,QAAQ,EAAE;MACRjB,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE;IACZ,CAAC;IACDc,SAAS,EAAE;MACTxB,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,sCAAsC;MAC3DrB,GAAG,EAAE,MAAM;MACXN,YAAY,EAAE;IAChB,CAAC;IACD4B,QAAQ,EAAE;MACR/B,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE,4BAA4B;MACvC3B,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBS,UAAU,EAAE,eAAe;MAC3BF,MAAM,EAAE,SAAS;MACjBF,MAAM,EAAE;IACV,CAAC;IACDqB,QAAQ,EAAE;MACRlB,QAAQ,EAAE,QAAQ;MAClBmB,WAAW,EAAE,MAAM;MACnBnC,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBF,KAAK,EAAE;IACT,CAAC;IACDwB,WAAW,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVtB,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACDmC,SAAS,EAAE;MACT3B,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,MAAM;MAChBW,UAAU,EAAE,KAAK;MACjBvB,YAAY,EAAE;IAChB,CAAC;IACDoC,eAAe,EAAE;MACf5B,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,SAAS;MACnByB,SAAS,EAAE;IACb,CAAC;IACDC,kBAAkB,EAAE;MAClBpC,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,sCAAsC;MAC3DrB,GAAG,EAAE,MAAM;MACXN,YAAY,EAAE;IAChB,CAAC;IACDuC,iBAAiB,EAAE;MACjB1C,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE,4BAA4B;MACvC3B,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBS,UAAU,EAAE,eAAe;MAC3BF,MAAM,EAAE,SAAS;MACjBF,MAAM,EAAE;IACV,CAAC;IACD+B,iBAAiB,EAAE;MACjB5B,QAAQ,EAAE,QAAQ;MAClBmB,WAAW,EAAE;IACf,CAAC;IACDU,oBAAoB,EAAE;MACpBR,IAAI,EAAE;IACR,CAAC;IACDS,mBAAmB,EAAE;MACnB9B,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACD2C,kBAAkB,EAAE;MAClBnC,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,SAAS;MACnBW,UAAU,EAAE;IACd,CAAC;IACDqB,gBAAgB,EAAE;MAChB1C,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,sCAAsC;MAC3DrB,GAAG,EAAE,MAAM;MACXN,YAAY,EAAE;IAChB,CAAC;IACD6C,UAAU,EAAE;MACVhD,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE,4BAA4B;MACvCX,SAAS,EAAE,QAAQ;MACnBL,UAAU,EAAE,qBAAqB;MACjCF,MAAM,EAAE,SAAS;MACjBmC,cAAc,EAAE,MAAM;MACtBtC,KAAK,EAAE;IACT,CAAC;IACDuC,UAAU,EAAE;MACVnC,QAAQ,EAAE,MAAM;MAChBJ,KAAK,EAAE,SAAS;MAChBR,YAAY,EAAE;IAChB,CAAC;IACDgD,WAAW,EAAE;MACXpC,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE;IAChB,CAAC;IACDiD,iBAAiB,EAAE;MACjBzC,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE;IACZ,CAAC;IACDsC,WAAW,EAAE;MACXhD,OAAO,EAAE,MAAM;MACfyB,mBAAmB,EAAE,SAAS;MAC9BrB,GAAG,EAAE;IACP,CAAC;IACD6C,IAAI,EAAE;MACJtD,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE;IACb,CAAC;IACDuB,SAAS,EAAE;MACTxC,QAAQ,EAAE,QAAQ;MAClBW,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE,MAAM;MACbR,YAAY,EAAE,MAAM;MACpBE,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE;IACd,CAAC;IACDiD,QAAQ,EAAE;MACRtB,WAAW,EAAE,MAAM;MACnBvB,KAAK,EAAE;IACT,CAAC;IACD8C,YAAY,EAAE;MACZ1D,OAAO,EAAE,MAAM;MACf2D,YAAY,EAAE,gBAAgB;MAC9BrD,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE;IACd,CAAC;IACDoD,YAAY,EAAE;MACZzB,WAAW,EAAE,MAAM;MACnBnB,QAAQ,EAAE;IACZ,CAAC;IACD6C,eAAe,EAAE;MACfxB,IAAI,EAAE;IACR,CAAC;IACDyB,eAAe,EAAE;MACflD,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE,QAAQ;MAClBZ,YAAY,EAAE;IAChB,CAAC;IACD2D,YAAY,EAAE;MACZnD,KAAK,EAAE,MAAM;MACbI,QAAQ,EAAE;IACZ,CAAC;IACDgD,SAAS,EAAE;MACThE,OAAO,EAAE,MAAM;MACfc,YAAY,EAAE,KAAK;MACnBV,YAAY,EAAE,MAAM;MACpB6D,UAAU,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACZvC,UAAU,EAAE,MAAM;MAClBf,KAAK,EAAE;IACT;EACF,CAAC;;EAED;EACA,MAAMuD,SAAS,GAAGlH,KAAK,GAAG,CACxB;IACEmH,IAAI,EAAEnI,MAAM;IACZoI,MAAM,EAAEpH,KAAK,CAACqH,KAAK,IAAI,CAAC;IACxBC,KAAK,EAAE,OAAO;IACd3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAElI,UAAU;IAChBmI,MAAM,EAAEpH,KAAK,CAACyH,QAAQ,IAAI,CAAC;IAC3BH,KAAK,EAAE,UAAU;IACjB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAEjI,YAAY;IAClBkI,MAAM,EAAEpH,KAAK,CAAC0H,OAAO,IAAI,CAAC;IAC1BJ,KAAK,EAAE,SAAS;IAChB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAEzI,eAAe;IACrB0I,MAAM,EAAEpH,KAAK,CAAC2H,SAAS,IAAI,CAAC;IAC5BL,KAAK,EAAE,WAAW;IAClB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAExI,aAAa;IACnByI,MAAM,EAAEpH,KAAK,CAAC4H,OAAO,IAAI,CAAC;IAC1BN,KAAK,EAAE,SAAS;IAChB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAE9H,QAAQ;IACd+H,MAAM,EAAEpH,KAAK,CAAC6H,QAAQ,IAAI,CAAC;IAC3BP,KAAK,EAAE,UAAU;IACjB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAE7H,QAAQ;IACd8H,MAAM,EAAEpH,KAAK,CAAC8H,YAAY,IAAI,CAAC;IAC/BR,KAAK,EAAE,cAAc;IACrB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,IAAI,EAAE1I,mBAAmB;IACzB2I,MAAM,EAAEpH,KAAK,CAAC+H,WAAW,IAAI,CAAC;IAC9BT,KAAK,EAAE,aAAa;IACpB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE;EACf,CAAC,CACF,GAAG,EAAE;;EAEN;EACA,MAAMQ,cAAc,GAAGhI,KAAK,GAAG,CAC7B;IACEmH,IAAI,EAAE3I,OAAO;IACb4I,MAAM,EAAEpH,KAAK,CAACiI,OAAO,IAAI,CAAC;IAC1BX,KAAK,EAAE,SAAS;IAChB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAEpI,qBAAqB;IAC3BqI,MAAM,EAAEpH,KAAK,CAACqC,mBAAmB,IAAI,CAAC;IACtCiF,KAAK,EAAE,sBAAsB;IAC7B3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE1H,OAAO;IACb2H,MAAM,EAAEpH,KAAK,CAACsC,kBAAkB,IAAI,CAAC;IACrCgF,KAAK,EAAE,qBAAqB;IAC5B3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAEvH,eAAe;IACrBwH,MAAM,EAAEpH,KAAK,CAACkI,gBAAgB,IAAI,CAAC;IACnCZ,KAAK,EAAE,kBAAkB;IACzB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAEtH,gBAAgB;IACtBuH,MAAM,EAAEpH,KAAK,CAACmI,WAAW,IAAI,CAAC;IAC9Bb,KAAK,EAAE,aAAa;IACpB3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAEzH,mBAAmB;IACzB0H,MAAM,EAAEpH,KAAK,CAAC+B,iBAAiB,IAAI,CAAC;IACpCuF,KAAK,EAAE,mBAAmB;IAC1B3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAExH,aAAa;IACnByH,MAAM,EAAEpH,KAAK,CAACoI,cAAc,IAAI,CAAC;IACjCd,KAAK,EAAE,sBAAsB;IAC7B3D,KAAK,EAAE,SAAS;IAChB4D,IAAI,EAAE;EACR,CAAC,CACF,GAAG,EAAE;EAEN,MAAMc,YAAY,GAAG,CACnB;IAAElB,IAAI,EAAE3I,OAAO;IAAE8J,KAAK,EAAE,wBAAwB;IAAEd,WAAW,EAAE,iDAAiD;IAAED,IAAI,EAAE;EAAgB,CAAC,EACzI;IAAEJ,IAAI,EAAE1I,mBAAmB;IAAE6J,KAAK,EAAE,iBAAiB;IAAEd,WAAW,EAAE,kCAAkC;IAAED,IAAI,EAAE;EAAS,CAAC,EACxH;IAAEJ,IAAI,EAAEzI,eAAe;IAAE4J,KAAK,EAAE,mBAAmB;IAAEd,WAAW,EAAE,gCAAgC;IAAED,IAAI,EAAE;EAAW,CAAC,EACtH;IAAEJ,IAAI,EAAEtI,KAAK;IAAEyJ,KAAK,EAAE,eAAe;IAAEd,WAAW,EAAE,qCAAqC;IAAED,IAAI,EAAE;EAAS,CAAC,EAC3G;IAAEJ,IAAI,EAAEzH,mBAAmB;IAAE4I,KAAK,EAAE,oBAAoB;IAAEd,WAAW,EAAE,uBAAuB;IAAED,IAAI,EAAE;EAAY,CAAC,EACnH;IAAEJ,IAAI,EAAExH,aAAa;IAAE2I,KAAK,EAAE,UAAU;IAAEd,WAAW,EAAE,sBAAsB;IAAED,IAAI,EAAE;EAAY,CAAC,CACnG;EAED,MAAMgB,aAAa,GAAGA,CAAA,KAAM;IAC1BzH,kBAAkB,CAAC,CAAC;EACtB,CAAC;EAED,IAAIR,OAAO,EAAE;IACX,oBACEtC,KAAA,CAAAwK,aAAA;MAAKC,KAAK,EAAE5F,MAAM,CAACC,SAAU;MAAA4F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3B/K,KAAA,CAAAwK,aAAA;MAAKC,KAAK,EAAE5F,MAAM,CAACsB,gBAAiB;MAAAuE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAClC/K,KAAA,CAAAwK,aAAA,CAACjJ,MAAM;MAACkJ,KAAK,EAAE5F,MAAM,CAACyB,WAAY;MAAAoE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eACrC/K,KAAA,CAAAwK,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,gCAAkC,CAAC,eACvC/K,KAAA,CAAAwK,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,sEAA8D,CAC9D,CACF,CAAC;EAEV;EAEA,oBACE/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACC,SAAU;IAAA4F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3B/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACK,MAAO;IAAAwF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACO,aAAc;IAAAsF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B/K,KAAA,CAAAwK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE/K,KAAA,CAAAwK,aAAA;IAAIC,KAAK,EAAE5F,MAAM,CAAC8B,WAAY;IAAA+D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B/K,KAAA,CAAAwK,aAAA,CAACjK,SAAS;IAACkK,KAAK,EAAE;MAAEvD,WAAW,EAAE,MAAM;MAAEvB,KAAK,EAAE;IAAU,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACpD,EAAC,CAAAhJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiJ,KAAK,KAAI,aACzB,CAAC,eACLhL,KAAA,CAAAwK,aAAA;IAAGC,KAAK,EAAE5F,MAAM,CAAC+B,QAAS;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+DAEzB,EAACrI,UAAU,IAAI,4BAA4BA,UAAU,EAAE,EACtDV,KAAK,IAAI,MAAMiJ,MAAM,CAACC,IAAI,CAAClJ,KAAK,CAAC,CAACmJ,MAAM,wBACxC,CACA,CAAC,eACNnL,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACW,aAAc;IAAAkF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B/K,KAAA,CAAAwK,aAAA;IACEY,OAAO,EAAEA,CAAA,KAAMvI,cAAc,CAAC,CAACD,WAAW,CAAE;IAC5C6H,KAAK,EAAE;MACL,GAAG5F,MAAM,CAACa,aAAa;MACvBV,eAAe,EAAEpC,WAAW,GAAG,SAAS,GAAG,SAAS;MACpDsE,WAAW,EAAE;IACf,CAAE;IACFoD,KAAK,EAAE1H,WAAW,GAAG,4CAA4C,GAAG,yCAA0C;IAAA8H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE7GnI,WAAW,GAAG,YAAY,GAAG,aACxB,CAAC,eACT5C,KAAA,CAAAwK,aAAA;IACEY,OAAO,EAAEb,aAAc;IACvBE,KAAK,EAAE5F,MAAM,CAACa,aAAc;IAC5B4E,KAAK,EAAC,sCAAmC;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzC/K,KAAA,CAAAwK,aAAA,CAACjJ,MAAM;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACJ,CACL,CACF,CAAC,EAELvI,KAAK,iBACJxC,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACoB,WAAY;IAAAyE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/K,KAAA,CAAAwK,aAAA,CAAChJ,OAAO;IAACiJ,KAAK,EAAE;MAAEvD,WAAW,EAAE;IAAO,CAAE;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,EAC1CvI,KACE,CAEJ,CAAC,eAGNxC,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAAC2B,aAAc;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B/K,KAAA,CAAAwK,aAAA;IAAIC,KAAK,EAAE5F,MAAM,CAAC4B,YAAa;IAAAiE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/K,KAAA,CAAAwK,aAAA,CAAC5J,UAAU;IAAC6J,KAAK,EAAE;MAAEvD,WAAW,EAAE,MAAM;MAAEvB,KAAK,EAAE;IAAU,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAE9D,CACD,CAAC,eAEN/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACgC,SAAU;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1B/I,KAAK,GACJkH,SAAS,CAACmC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBvL,KAAA,CAAAwK,aAAA,CAACnK,IAAI;IACHmL,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEH,IAAI,CAAC/B,IAAI,IAAI,GAAI;IACrBkB,KAAK,EAAE;MAAExC,cAAc,EAAE,MAAM;MAAEtC,KAAK,EAAE;IAAU,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpD/K,KAAA,CAAAwK,aAAA;IACEC,KAAK,EAAE5F,MAAM,CAACkC,QAAS;IACvB2E,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACoB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACoB,SAAS,GAAG,eAAgB;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvE/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAG5F,MAAM,CAACoC,QAAQ;MAAEjC,eAAe,EAAEsG,IAAI,CAAC3F;IAAK,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5D/K,KAAA,CAAAwK,aAAA,CAACc,IAAI,CAACnC,IAAI;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACN/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACsC,WAAY;IAAAuD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACwC,UAAW;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEO,IAAI,CAAClC,MAAY,CAAC,eAClDpJ,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACyC,SAAU;IAAAoD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEO,IAAI,CAAChC,KAAW,CAAC,EAC/CgC,IAAI,CAAC9B,WAAW,iBACfxJ,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAAC0C,eAAgB;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEO,IAAI,CAAC9B,WAAiB,CAE1D,CACF,CACD,CACP,CAAC,gBAEFxJ,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE;MACVsB,UAAU,EAAE,QAAQ;MACpB1F,SAAS,EAAE,QAAQ;MACnBtB,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,OAAO;MACxBa,YAAY,EAAE,MAAM;MACpBmB,SAAS,EAAE,4BAA4B;MACvCpB,MAAM,EAAE;IACV,CAAE;IAAA8E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACA/K,KAAA,CAAAwK,aAAA,CAACzJ,qBAAqB;IAAC0J,KAAK,EAAE;MAAE1E,QAAQ,EAAE,MAAM;MAAEJ,KAAK,EAAE,SAAS;MAAER,YAAY,EAAE;IAAO,CAAE;IAAAuF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC9F/K,KAAA,CAAAwK,aAAA;IAAIC,KAAK,EAAE;MAAE9E,KAAK,EAAE,SAAS;MAAER,YAAY,EAAE;IAAO,CAAE;IAAAuF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6BAA4B,CAAC,eACpF/K,KAAA,CAAAwK,aAAA;IAAGC,KAAK,EAAE;MAAE9E,KAAK,EAAE,MAAM;MAAER,YAAY,EAAE;IAAO,CAAE;IAAAuF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sEAEjD,eAAA/K,KAAA,CAAAwK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,8DACL,CAAC,eACJ/K,KAAA,CAAAwK,aAAA;IACEY,OAAO,EAAEb,aAAc;IACvBE,KAAK,EAAE;MACL,GAAG5F,MAAM,CAACa,aAAa;MACvBV,eAAe,EAAE,SAAS;MAC1Be,QAAQ,EAAE,MAAM;MAChBhB,OAAO,EAAE;IACX,CAAE;IAAA2F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF/K,KAAA,CAAAwK,aAAA,CAACjJ,MAAM;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,iBACJ,CACL,CAEJ,CAAC,eAGN/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAAC2B,aAAc;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B/K,KAAA,CAAAwK,aAAA;IAAIC,KAAK,EAAE5F,MAAM,CAAC4B,YAAa;IAAAiE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/K,KAAA,CAAAwK,aAAA,CAAC1J,aAAa;IAAC2J,KAAK,EAAE;MAAEvD,WAAW,EAAE,MAAM;MAAEvB,KAAK,EAAE;IAAU,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAEjE,CACD,CAAC,EAEL/I,KAAK,iBACJhC,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAAC4C,kBAAmB;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnCf,cAAc,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BvL,KAAA,CAAAwK,aAAA,CAACnK,IAAI;IACHmL,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEH,IAAI,CAAC/B,IAAI,IAAI,GAAI;IACrBkB,KAAK,EAAE;MAAExC,cAAc,EAAE,MAAM;MAAEtC,KAAK,EAAE;IAAU,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpD/K,KAAA,CAAAwK,aAAA;IACEC,KAAK,EAAE5F,MAAM,CAAC6C,iBAAkB;IAChCgE,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACoB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACoB,SAAS,GAAG,eAAgB;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvE/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAG5F,MAAM,CAAC8C,iBAAiB;MAAEhC,KAAK,EAAE2F,IAAI,CAAC3F;IAAK,CAAE;IAAA+E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3D/K,KAAA,CAAAwK,aAAA,CAACc,IAAI,CAACnC,IAAI;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACN/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAAC+C,oBAAqB;IAAA8C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtC/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACgD,mBAAoB;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEO,IAAI,CAAClC,MAAY,CAAC,eAC3DpJ,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACiD,kBAAmB;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEO,IAAI,CAAChC,KAAW,CACrD,CACF,CACD,CACP,CACE,CACN,eAGDtJ,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACkD,gBAAiB;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCV,YAAY,CAACgB,GAAG,CAAC,CAACW,MAAM,EAAET,KAAK,kBAC9BvL,KAAA,CAAAwK,aAAA,CAACnK,IAAI;IACHmL,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEO,MAAM,CAACzC,IAAK;IAChBkB,KAAK,EAAE5F,MAAM,CAACmD,UAAW;IACzB0D,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACoB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACnB,KAAK,CAACoB,SAAS,GAAG,eAAgB;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvE/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACqD,UAAW;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B/K,KAAA,CAAAwK,aAAA,CAACwB,MAAM,CAAC7C,IAAI;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACX,CAAC,eACN/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACsD,WAAY;IAAAuC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEiB,MAAM,CAAC1B,KAAW,CAAC,eACpDtK,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACuD,iBAAkB;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEiB,MAAM,CAACxC,WAAiB,CAC3D,CACP,CACE,CAAC,eAGNxJ,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACwD,WAAY;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7B/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACyD,IAAK;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB/K,KAAA,CAAAwK,aAAA;IAAIC,KAAK,EAAE5F,MAAM,CAAC0D,SAAU;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B/K,KAAA,CAAAwK,aAAA,CAAC5J,UAAU;IAAC6J,KAAK,EAAE5F,MAAM,CAAC2D,QAAS;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAEpC,CAAC,EACJ7I,gBAAgB,CAACmJ,GAAG,CAACY,QAAQ,iBAC5BjM,KAAA,CAAAwK,aAAA;IAAKgB,GAAG,EAAES,QAAQ,CAAChI,EAAG;IAACwG,KAAK,EAAE5F,MAAM,CAAC4D,YAAa;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChD/K,KAAA,CAAAwK,aAAA;IAAMC,KAAK,EAAE5F,MAAM,CAAC8D,YAAa;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BpG,eAAe,CAACsH,QAAQ,CAAC/H,IAAI,CAC1B,CAAC,eACPlE,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAAC+D,eAAgB;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACgE,eAAgB;IAAA6B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEkB,QAAQ,CAAC9H,OAAa,CAAC,eAC5DnE,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACiE,YAAa;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEkB,QAAQ,CAACC,IAAU,CAClD,CACF,CACN,CACE,CAAC,eAGNlM,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACyD,IAAK;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB/K,KAAA,CAAAwK,aAAA;IAAIC,KAAK,EAAE5F,MAAM,CAAC0D,SAAU;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B/K,KAAA,CAAAwK,aAAA,CAACzJ,qBAAqB;IAAC0J,KAAK,EAAE5F,MAAM,CAAC2D,QAAS;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,2BAE/C,CAAC,EACJ3I,OAAO,CAACiJ,GAAG,CAACc,MAAM,iBACjBnM,KAAA,CAAAwK,aAAA;IACEgB,GAAG,EAAEW,MAAM,CAAClI,EAAG;IACfwG,KAAK,EAAE;MACL,GAAG5F,MAAM,CAACkE,SAAS;MACnBqD,eAAe,EAAExH,gBAAgB,CAACuH,MAAM,CAAC/H,QAAQ,CAAC;MAClDY,eAAe,EAAE,GAAGJ,gBAAgB,CAACuH,MAAM,CAAC/H,QAAQ,CAAC;IACvD,CAAE;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF/K,KAAA,CAAAwK,aAAA;IAAKC,KAAK,EAAE5F,MAAM,CAACoE,YAAa;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEoB,MAAM,CAAChI,OAAa,CACnD,CACN,CACE,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAerC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}