import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { Link } from 'react-router-dom';
import axios from 'axios';
import {
  FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap,
  FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle,
  FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck,
  FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar,
  FaCertificate, FaClipboardList, FaQuestionCircle
} from 'react-icons/fa';

const ResponsableDashboard = () => {
  const { user } = useContext(AuthContext);
  const [stats, setStats] = useState({
    cours: 0,
    matieres: 0,
    groupes: 0,
    etudiants: 0,
    parents: 0,
    filieres: 0,
    utilisateurs: 0,
    enseignants: 0,
    classes: 0,
    niveaux: 0,
    absences_aujourdhui: 0,
    retards_aujourdhui: 0,
    devoirs_en_cours: 0,
    quiz_actifs: 0,
    factures_impayees: 0,
    diplomes_annee: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [alertes, setAlertes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement des statistiques du dashboard...');

      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('📊 Réponse API stats:', response.data);

      if (response.data.success) {
        setStats(response.data.stats);

        // Traiter les activités récentes
        if (response.data.stats.activites_recentes) {
          setRecentActivities(response.data.stats.activites_recentes);
        }

        // Générer des alertes basées sur les données
        const newAlertes = [];

        if (response.data.stats.factures_impayees > 0) {
          newAlertes.push({
            id: 1,
            type: 'warning',
            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,
            priority: 'high'
          });
        }

        if (response.data.stats.absences_aujourdhui > 10) {
          newAlertes.push({
            id: 2,
            type: 'urgent',
            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,
            priority: 'high'
          });
        }

        if (response.data.stats.retards_aujourdhui > 5) {
          newAlertes.push({
            id: 3,
            type: 'info',
            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,
            priority: 'medium'
          });
        }

        newAlertes.push({
          id: 4,
          type: 'success',
          message: 'Système opérationnel - Toutes les données sont à jour',
          priority: 'low'
        });

        setAlertes(newAlertes);

        console.log('✅ Statistiques chargées avec succès');
      } else {
        throw new Error(response.data.error || 'Erreur lors du chargement des statistiques');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des données:', error);
      setError('Impossible de charger les statistiques. Vérifiez votre connexion.');

      // Données de fallback en cas d'erreur
      setStats({
        cours: 0, matieres: 0, groupes: 0, etudiants: 0, parents: 0,
        filieres: 0, utilisateurs: 0, enseignants: 0, classes: 0, niveaux: 0,
        absences_aujourdhui: 0, retards_aujourdhui: 0, devoirs_en_cours: 0,
        quiz_actifs: 0, factures_impayees: 0, diplomes_annee: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type) => {
    switch(type) {
      case 'inscription': return '👤';
      case 'note': return '📝';
      case 'absence': return '⚠️';
      case 'enseignant': return '👨‍🏫';
      default: return '📢';
    }
  };

  const getPriorityColor = (priority) => {
    switch(priority) {
      case 'high': return '#dc3545';
      case 'medium': return '#ffc107';
      case 'low': return '#28a745';
      default: return '#6c757d';
    }
  };

  const styles = {
    container: {
      padding: '20px',
      backgroundColor: '#f8f9fa',
      minHeight: '100vh',
    },
    header: {
      marginBottom: '30px',
    },
    headerContent: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '15px',
    },
    headerActions: {
      display: 'flex',
      gap: '10px',
    },
    refreshButton: {
      backgroundColor: '#007bff',
      color: 'white',
      border: 'none',
      padding: '10px 20px',
      borderRadius: '8px',
      cursor: 'pointer',
      fontSize: '14px',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      transition: 'background-color 0.3s ease',
    },
    errorBanner: {
      backgroundColor: '#f8d7da',
      color: '#721c24',
      padding: '12px 20px',
      borderRadius: '8px',
      border: '1px solid #f5c6cb',
      display: 'flex',
      alignItems: 'center',
      marginTop: '15px',
    },
    loadingContainer: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '400px',
      textAlign: 'center',
    },
    loadingIcon: {
      fontSize: '3rem',
      color: '#007bff',
      animation: 'spin 2s linear infinite',
      marginBottom: '20px',
    },
    sectionHeader: {
      marginBottom: '20px',
    },
    sectionTitle: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#333',
      display: 'flex',
      alignItems: 'center',
      marginBottom: '10px',
    },
    welcomeText: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    subtitle: {
      color: '#666',
      fontSize: '1.1rem',
    },
    statsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
      gap: '20px',
      marginBottom: '40px',
    },
    statCard: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '12px',
      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
      display: 'flex',
      alignItems: 'center',
      transition: 'all 0.3s ease',
      cursor: 'pointer',
      border: '1px solid #e9ecef',
    },
    statIcon: {
      fontSize: '2.5rem',
      marginRight: '20px',
      padding: '15px',
      borderRadius: '12px',
      color: 'white',
    },
    statContent: {
      flex: 1,
    },
    statNumber: {
      fontSize: '2.2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '5px',
    },
    statLabel: {
      color: '#666',
      fontSize: '1rem',
      fontWeight: '500',
      marginBottom: '3px',
    },
    statDescription: {
      color: '#999',
      fontSize: '0.85rem',
      fontStyle: 'italic',
    },
    secondaryStatsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '15px',
      marginBottom: '40px',
    },
    secondaryStatCard: {
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '10px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
      display: 'flex',
      alignItems: 'center',
      transition: 'all 0.3s ease',
      cursor: 'pointer',
      border: '1px solid #f1f3f4',
    },
    secondaryStatIcon: {
      fontSize: '1.8rem',
      marginRight: '15px',
    },
    secondaryStatContent: {
      flex: 1,
    },
    secondaryStatNumber: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '3px',
    },
    secondaryStatLabel: {
      color: '#666',
      fontSize: '0.85rem',
      fontWeight: '500',
    },
    quickActionsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '20px',
      marginBottom: '30px',
    },
    actionCard: {
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      textAlign: 'center',
      transition: 'transform 0.3s ease',
      cursor: 'pointer',
      textDecoration: 'none',
      color: 'inherit',
    },
    actionIcon: {
      fontSize: '2rem',
      color: '#007bff',
      marginBottom: '15px',
    },
    actionTitle: {
      fontSize: '1.1rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    actionDescription: {
      color: '#666',
      fontSize: '0.9rem',
    },
    contentGrid: {
      display: 'grid',
      gridTemplateColumns: '2fr 1fr',
      gap: '30px',
    },
    card: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    },
    cardTitle: {
      fontSize: '1.3rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center',
    },
    cardIcon: {
      marginRight: '10px',
      color: '#007bff',
    },
    activityItem: {
      padding: '12px',
      borderBottom: '1px solid #eee',
      display: 'flex',
      alignItems: 'center',
    },
    activityIcon: {
      marginRight: '15px',
      fontSize: '1.2rem',
    },
    activityContent: {
      flex: 1,
    },
    activityMessage: {
      color: '#333',
      fontSize: '0.9rem',
      marginBottom: '4px',
    },
    activityTime: {
      color: '#999',
      fontSize: '0.8rem',
    },
    alertItem: {
      padding: '15px',
      borderRadius: '8px',
      marginBottom: '15px',
      borderLeft: '4px solid',
    },
    alertMessage: {
      fontWeight: 'bold',
      color: '#333',
    },
  };

  const statCards = [
    // Statistiques principales demandées
    {
      icon: FaBook,
      number: stats.cours,
      label: 'Cours',
      color: '#007bff',
      link: '/cours',
      description: 'Total des cours disponibles'
    },
    {
      icon: FaBookOpen,
      number: stats.matieres,
      label: 'Matières',
      color: '#28a745',
      link: '/matieres',
      description: 'Matières enseignées'
    },
    {
      icon: FaLayerGroup,
      number: stats.groupes,
      label: 'Groupes',
      color: '#17a2b8',
      link: '/groupes',
      description: 'Groupes d\'étudiants'
    },
    {
      icon: FaGraduationCap,
      number: stats.etudiants,
      label: 'Étudiants',
      color: '#6f42c1',
      link: '/etudiants',
      description: 'Étudiants inscrits'
    },
    {
      icon: FaUserFriends,
      number: stats.parents,
      label: 'Parents',
      color: '#fd7e14',
      link: '/parents',
      description: 'Parents d\'étudiants'
    },
    {
      icon: FaStream,
      number: stats.filieres,
      label: 'Filières',
      color: '#e83e8c',
      link: '/filieres',
      description: 'Filières d\'études'
    },
    {
      icon: FaIdCard,
      number: stats.utilisateurs,
      label: 'Utilisateurs',
      color: '#20c997',
      link: '/utilisateurs',
      description: 'Utilisateurs du système'
    },
    {
      icon: FaChalkboardTeacher,
      number: stats.enseignants,
      label: 'Enseignants',
      color: '#ffc107',
      link: '/enseignants',
      description: 'Professeurs actifs'
    },
  ];

  // Statistiques secondaires pour le monitoring
  const secondaryStats = [
    {
      icon: FaUsers,
      number: stats.classes,
      label: 'Classes',
      color: '#6c757d',
      link: '/classes'
    },
    {
      icon: FaExclamationTriangle,
      number: stats.absences_aujourdhui,
      label: "Absences Aujourd'hui",
      color: '#dc3545',
      link: '/absences'
    },
    {
      icon: FaClock,
      number: stats.retards_aujourdhui,
      label: "Retards Aujourd'hui",
      color: '#fd7e14',
      link: '/retards'
    },
    {
      icon: FaClipboardList,
      number: stats.devoirs_en_cours,
      label: "Devoirs en Cours",
      color: '#17a2b8',
      link: '/devoirs'
    },
    {
      icon: FaQuestionCircle,
      number: stats.quiz_actifs,
      label: "Quiz Actifs",
      color: '#6f42c1',
      link: '/quiz'
    },
    {
      icon: FaFileInvoiceDollar,
      number: stats.factures_impayees,
      label: "Factures Impayées",
      color: '#dc3545',
      link: '/factures'
    },
    {
      icon: FaCertificate,
      number: stats.diplomes_annee,
      label: "Diplômes Cette Année",
      color: '#28a745',
      link: '/diplomes'
    },
  ];

  const quickActions = [
    { icon: FaUsers, title: 'Gérer les Utilisateurs', description: 'Ajouter, modifier ou supprimer des utilisateurs', link: '/utilisateurs' },
    { icon: FaChalkboardTeacher, title: 'Gérer les Cours', description: 'Planifier et organiser les cours', link: '/cours' },
    { icon: FaGraduationCap, title: 'Gérer les Classes', description: 'Créer et organiser les classes', link: '/classes' },
    { icon: FaCog, title: 'Configuration', description: 'Paramètres système et configuration', link: '/roles' },
    { icon: FaFileInvoiceDollar, title: 'Gestion Financière', description: 'Factures et paiements', link: '/factures' },
    { icon: FaCertificate, title: 'Diplômes', description: 'Gestion des diplômes', link: '/diplomes' },
  ];

  const handleRefresh = () => {
    fetchDashboardData();
  };

  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingContainer}>
          <FaSync style={styles.loadingIcon} />
          <h2>Chargement des statistiques...</h2>
          <p>Veuillez patienter pendant que nous récupérons les données.</p>
        </div>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      {/* En-tête amélioré */}
      <div style={styles.header}>
        <div style={styles.headerContent}>
          <div>
            <h1 style={styles.welcomeText}>
              <FaUserTie style={{ marginRight: '15px', color: '#007bff' }} />
              Bienvenue, {user?.email || 'Responsable'}
            </h1>
            <p style={styles.subtitle}>Tableau de bord administrateur - Vue d'ensemble de l'école</p>
          </div>
          <div style={styles.headerActions}>
            <button
              onClick={handleRefresh}
              style={styles.refreshButton}
              title="Actualiser les données"
            >
              <FaSync /> Actualiser
            </button>
          </div>
        </div>

        {error && (
          <div style={styles.errorBanner}>
            <FaTimes style={{ marginRight: '10px' }} />
            {error}
          </div>
        )}
      </div>

      {/* Statistiques principales */}
      <div style={styles.sectionHeader}>
        <h2 style={styles.sectionTitle}>
          <FaChartBar style={{ marginRight: '10px', color: '#007bff' }} />
          Statistiques Principales
        </h2>
      </div>

      <div style={styles.statsGrid}>
        {statCards.map((stat, index) => (
          <Link
            key={index}
            to={stat.link || '#'}
            style={{ textDecoration: 'none', color: 'inherit' }}
          >
            <div
              style={styles.statCard}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              <div style={{...styles.statIcon, backgroundColor: stat.color}}>
                <stat.icon />
              </div>
              <div style={styles.statContent}>
                <div style={styles.statNumber}>{stat.number}</div>
                <div style={styles.statLabel}>{stat.label}</div>
                {stat.description && (
                  <div style={styles.statDescription}>{stat.description}</div>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Statistiques secondaires */}
      <div style={styles.sectionHeader}>
        <h2 style={styles.sectionTitle}>
          <FaCalendarAlt style={{ marginRight: '10px', color: '#28a745' }} />
          Monitoring & Activité
        </h2>
      </div>

      <div style={styles.secondaryStatsGrid}>
        {secondaryStats.map((stat, index) => (
          <Link
            key={index}
            to={stat.link || '#'}
            style={{ textDecoration: 'none', color: 'inherit' }}
          >
            <div
              style={styles.secondaryStatCard}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-3px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              <div style={{...styles.secondaryStatIcon, color: stat.color}}>
                <stat.icon />
              </div>
              <div style={styles.secondaryStatContent}>
                <div style={styles.secondaryStatNumber}>{stat.number}</div>
                <div style={styles.secondaryStatLabel}>{stat.label}</div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Actions rapides */}
      <div style={styles.quickActionsGrid}>
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            style={styles.actionCard}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
          >
            <div style={styles.actionIcon}>
              <action.icon />
            </div>
            <div style={styles.actionTitle}>{action.title}</div>
            <div style={styles.actionDescription}>{action.description}</div>
          </Link>
        ))}
      </div>

      {/* Contenu principal */}
      <div style={styles.contentGrid}>
        {/* Activités récentes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaChartBar style={styles.cardIcon} />
            Activités Récentes
          </h2>
          {recentActivities.map(activity => (
            <div key={activity.id} style={styles.activityItem}>
              <span style={styles.activityIcon}>
                {getActivityIcon(activity.type)}
              </span>
              <div style={styles.activityContent}>
                <div style={styles.activityMessage}>{activity.message}</div>
                <div style={styles.activityTime}>{activity.time}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Alertes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaExclamationTriangle style={styles.cardIcon} />
            Alertes & Notifications
          </h2>
          {alertes.map(alerte => (
            <div 
              key={alerte.id} 
              style={{
                ...styles.alertItem,
                borderLeftColor: getPriorityColor(alerte.priority),
                backgroundColor: `${getPriorityColor(alerte.priority)}10`
              }}
            >
              <div style={styles.alertMessage}>{alerte.message}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ResponsableDashboard;
