{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\dashboards\\\\ResponsableDashboard.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap, FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle, FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck, FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar, FaCertificate, FaClipboardList, FaQuestionCircle } from 'react-icons/fa';\nconst ResponsableDashboard = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    cours: 0,\n    matieres: 0,\n    groupes: 0,\n    etudiants: 0,\n    parents: 0,\n    filieres: 0,\n    utilisateurs: 0,\n    enseignants: 0,\n    classes: 0,\n    niveaux: 0,\n    absences_aujourdhui: 0,\n    retards_aujourdhui: 0,\n    devoirs_en_cours: 0,\n    quiz_actifs: 0,\n    factures_impayees: 0,\n    diplomes_annee: 0\n  });\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🔄 Chargement des statistiques du dashboard...');\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('📊 Réponse API stats:', response.data);\n      if (response.data.success) {\n        setStats(response.data.stats);\n\n        // Traiter les activités récentes\n        if (response.data.stats.activites_recentes) {\n          setRecentActivities(response.data.stats.activites_recentes);\n        }\n\n        // Générer des alertes basées sur les données\n        const newAlertes = [];\n        if (response.data.stats.factures_impayees > 0) {\n          newAlertes.push({\n            id: 1,\n            type: 'warning',\n            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,\n            priority: 'high'\n          });\n        }\n        if (response.data.stats.absences_aujourdhui > 10) {\n          newAlertes.push({\n            id: 2,\n            type: 'urgent',\n            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,\n            priority: 'high'\n          });\n        }\n        if (response.data.stats.retards_aujourdhui > 5) {\n          newAlertes.push({\n            id: 3,\n            type: 'info',\n            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,\n            priority: 'medium'\n          });\n        }\n        newAlertes.push({\n          id: 4,\n          type: 'success',\n          message: 'Système opérationnel - Toutes les données sont à jour',\n          priority: 'low'\n        });\n        setAlertes(newAlertes);\n        console.log('✅ Statistiques chargées avec succès');\n      } else {\n        throw new Error(response.data.error || 'Erreur lors du chargement des statistiques');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données:', error);\n      setError('Impossible de charger les statistiques. Vérifiez votre connexion.');\n\n      // Données de fallback en cas d'erreur\n      setStats({\n        cours: 0,\n        matieres: 0,\n        groupes: 0,\n        etudiants: 0,\n        parents: 0,\n        filieres: 0,\n        utilisateurs: 0,\n        enseignants: 0,\n        classes: 0,\n        niveaux: 0,\n        absences_aujourdhui: 0,\n        retards_aujourdhui: 0,\n        devoirs_en_cours: 0,\n        quiz_actifs: 0,\n        factures_impayees: 0,\n        diplomes_annee: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'inscription':\n        return '👤';\n      case 'note':\n        return '📝';\n      case 'absence':\n        return '⚠️';\n      case 'enseignant':\n        return '👨‍🏫';\n      default:\n        return '📢';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return '#dc3545';\n      case 'medium':\n        return '#ffc107';\n      case 'low':\n        return '#28a745';\n      default:\n        return '#6c757d';\n    }\n  };\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    header: {\n      marginBottom: '30px'\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem'\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer'\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '50%',\n      color: 'white'\n    },\n    statContent: {\n      flex: 1\n    },\n    statNumber: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px'\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px'\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px'\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem'\n    },\n    activityContent: {\n      flex: 1\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px'\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem'\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid'\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333'\n    }\n  };\n  const statCards = [\n  // Statistiques principales demandées\n  {\n    icon: FaBook,\n    number: stats.cours,\n    label: 'Cours',\n    color: '#007bff',\n    link: '/cours',\n    description: 'Total des cours disponibles'\n  }, {\n    icon: FaBookOpen,\n    number: stats.matieres,\n    label: 'Matières',\n    color: '#28a745',\n    link: '/matieres',\n    description: 'Matières enseignées'\n  }, {\n    icon: FaLayerGroup,\n    number: stats.groupes,\n    label: 'Groupes',\n    color: '#17a2b8',\n    link: '/groupes',\n    description: 'Groupes d\\'étudiants'\n  }, {\n    icon: FaGraduationCap,\n    number: stats.etudiants,\n    label: 'Étudiants',\n    color: '#6f42c1',\n    link: '/etudiants',\n    description: 'Étudiants inscrits'\n  }, {\n    icon: FaUserFriends,\n    number: stats.parents,\n    label: 'Parents',\n    color: '#fd7e14',\n    link: '/parents',\n    description: 'Parents d\\'étudiants'\n  }, {\n    icon: FaStream,\n    number: stats.filieres,\n    label: 'Filières',\n    color: '#e83e8c',\n    link: '/filieres',\n    description: 'Filières d\\'études'\n  }, {\n    icon: FaIdCard,\n    number: stats.utilisateurs,\n    label: 'Utilisateurs',\n    color: '#20c997',\n    link: '/utilisateurs',\n    description: 'Utilisateurs du système'\n  }, {\n    icon: FaChalkboardTeacher,\n    number: stats.enseignants,\n    label: 'Enseignants',\n    color: '#ffc107',\n    link: '/enseignants',\n    description: 'Professeurs actifs'\n  }];\n\n  // Statistiques secondaires pour le monitoring\n  const secondaryStats = [{\n    icon: FaUsers,\n    number: stats.classes,\n    label: 'Classes',\n    color: '#6c757d',\n    link: '/classes'\n  }, {\n    icon: FaExclamationTriangle,\n    number: stats.absences_aujourdhui,\n    label: \"Absences Aujourd'hui\",\n    color: '#dc3545',\n    link: '/absences'\n  }, {\n    icon: FaClock,\n    number: stats.retards_aujourdhui,\n    label: \"Retards Aujourd'hui\",\n    color: '#fd7e14',\n    link: '/retards'\n  }, {\n    icon: FaClipboardList,\n    number: stats.devoirs_en_cours,\n    label: \"Devoirs en Cours\",\n    color: '#17a2b8',\n    link: '/devoirs'\n  }, {\n    icon: FaQuestionCircle,\n    number: stats.quiz_actifs,\n    label: \"Quiz Actifs\",\n    color: '#6f42c1',\n    link: '/quiz'\n  }, {\n    icon: FaFileInvoiceDollar,\n    number: stats.factures_impayees,\n    label: \"Factures Impayées\",\n    color: '#dc3545',\n    link: '/factures'\n  }, {\n    icon: FaCertificate,\n    number: stats.diplomes_annee,\n    label: \"Diplômes Cette Année\",\n    color: '#28a745',\n    link: '/diplomes'\n  }];\n  const quickActions = [{\n    icon: FaUsers,\n    title: 'Gérer les Utilisateurs',\n    description: 'Ajouter, modifier ou supprimer des utilisateurs',\n    link: '/registers'\n  }, {\n    icon: FaChalkboardTeacher,\n    title: 'Gérer les Cours',\n    description: 'Planifier et organiser les cours',\n    link: '/cours'\n  }, {\n    icon: FaGraduationCap,\n    title: 'Gérer les Classes',\n    description: 'Créer et organiser les classes',\n    link: '/classes'\n  }, {\n    icon: FaCog,\n    title: 'Configuration',\n    description: 'Paramètres système et configuration',\n    link: '/roles'\n  }];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.welcomeText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaUserTie, {\n    style: {\n      marginRight: '15px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 11\n    }\n  }), \"Bienvenue, \", (user === null || user === void 0 ? void 0 : user.email) || 'Responsable'), /*#__PURE__*/React.createElement(\"p\", {\n    style: styles.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 9\n    }\n  }, \"Tableau de bord administrateur - Vue d'ensemble de l'\\xE9cole\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }\n  }, statCards.map((stat, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: stat.link || '#',\n    style: {\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.statIcon,\n      backgroundColor: stat.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 17\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 17\n    }\n  }, stat.label)))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.quickActionsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }\n  }, quickActions.map((action, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: action.link,\n    style: styles.actionCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(action.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 13\n    }\n  }, action.title), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionDescription,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 13\n    }\n  }, action.description)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.contentGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaChartBar, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 13\n    }\n  }), \"Activit\\xE9s R\\xE9centes\"), recentActivities.map(activity => /*#__PURE__*/React.createElement(\"div\", {\n    key: activity.id,\n    style: styles.activityItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.activityIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 15\n    }\n  }, getActivityIcon(activity.type)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 17\n    }\n  }, activity.message), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityTime,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 17\n    }\n  }, activity.time))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 13\n    }\n  }), \"Alertes & Notifications\"), alertes.map(alerte => /*#__PURE__*/React.createElement(\"div\", {\n    key: alerte.id,\n    style: {\n      ...styles.alertItem,\n      borderLeftColor: getPriorityColor(alerte.priority),\n      backgroundColor: `${getPriorityColor(alerte.priority)}10`\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.alertMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 15\n    }\n  }, alerte.message))))));\n};\nexport default ResponsableDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "Link", "axios", "FaUserTie", "FaUsers", "FaChalkboardTeacher", "FaGraduationCap", "FaUserFriends", "FaChartBar", "FaCog", "FaCalendarAlt", "FaExclamationTriangle", "FaBook", "FaBookOpen", "FaLayerGroup", "FaUserGraduate", "FaUserCheck", "FaStream", "FaIdCard", "FaSync", "FaTimes", "FaClock", "FaFileInvoiceDollar", "FaCertificate", "FaClipboardList", "FaQuestionCircle", "ResponsableDashboard", "user", "stats", "setStats", "cours", "matieres", "groupes", "etudiants", "parents", "filieres", "utilisateurs", "enseignants", "classes", "niveaux", "absences_aujourdhui", "retards_aujourdhui", "devoirs_en_cours", "quiz_actifs", "factures_impayees", "diplomes_annee", "recentActivities", "setRecentActivities", "alertes", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "fetchDashboardData", "console", "log", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "success", "activites_recentes", "newAlertes", "push", "id", "type", "message", "priority", "Error", "getActivityIcon", "getPriorityColor", "styles", "container", "padding", "backgroundColor", "minHeight", "header", "marginBottom", "welcomeText", "fontSize", "fontWeight", "color", "subtitle", "statsGrid", "display", "gridTemplateColumns", "gap", "statCard", "borderRadius", "boxShadow", "alignItems", "transition", "cursor", "statIcon", "marginRight", "statContent", "flex", "statNumber", "statLabel", "quickActionsGrid", "actionCard", "textAlign", "textDecoration", "actionIcon", "actionTitle", "actionDescription", "contentGrid", "card", "cardTitle", "cardIcon", "activityItem", "borderBottom", "activityIcon", "activityContent", "activityMessage", "activityTime", "alertItem", "borderLeft", "alertMessage", "statCards", "icon", "number", "label", "link", "description", "secondaryStats", "quickActions", "title", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "map", "stat", "index", "key", "to", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "action", "activity", "time", "alerte", "borderLeftColor"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/dashboards/ResponsableDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap,\n  FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle,\n  FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck,\n  FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar,\n  FaCertificate, FaClipboardList, FaQuestionCircle\n} from 'react-icons/fa';\n\nconst ResponsableDashboard = () => {\n  const { user } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    cours: 0,\n    matieres: 0,\n    groupes: 0,\n    etudiants: 0,\n    parents: 0,\n    filieres: 0,\n    utilisateurs: 0,\n    enseignants: 0,\n    classes: 0,\n    niveaux: 0,\n    absences_aujourdhui: 0,\n    retards_aujourdhui: 0,\n    devoirs_en_cours: 0,\n    quiz_actifs: 0,\n    factures_impayees: 0,\n    diplomes_annee: 0\n  });\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🔄 Chargement des statistiques du dashboard...');\n\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/dashboard/stats.php', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      console.log('📊 Réponse API stats:', response.data);\n\n      if (response.data.success) {\n        setStats(response.data.stats);\n\n        // Traiter les activités récentes\n        if (response.data.stats.activites_recentes) {\n          setRecentActivities(response.data.stats.activites_recentes);\n        }\n\n        // Générer des alertes basées sur les données\n        const newAlertes = [];\n\n        if (response.data.stats.factures_impayees > 0) {\n          newAlertes.push({\n            id: 1,\n            type: 'warning',\n            message: `${response.data.stats.factures_impayees} facture(s) en attente de paiement`,\n            priority: 'high'\n          });\n        }\n\n        if (response.data.stats.absences_aujourdhui > 10) {\n          newAlertes.push({\n            id: 2,\n            type: 'urgent',\n            message: `Nombre élevé d'absences aujourd'hui: ${response.data.stats.absences_aujourdhui}`,\n            priority: 'high'\n          });\n        }\n\n        if (response.data.stats.retards_aujourdhui > 5) {\n          newAlertes.push({\n            id: 3,\n            type: 'info',\n            message: `${response.data.stats.retards_aujourdhui} retard(s) signalé(s) aujourd'hui`,\n            priority: 'medium'\n          });\n        }\n\n        newAlertes.push({\n          id: 4,\n          type: 'success',\n          message: 'Système opérationnel - Toutes les données sont à jour',\n          priority: 'low'\n        });\n\n        setAlertes(newAlertes);\n\n        console.log('✅ Statistiques chargées avec succès');\n      } else {\n        throw new Error(response.data.error || 'Erreur lors du chargement des statistiques');\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des données:', error);\n      setError('Impossible de charger les statistiques. Vérifiez votre connexion.');\n\n      // Données de fallback en cas d'erreur\n      setStats({\n        cours: 0, matieres: 0, groupes: 0, etudiants: 0, parents: 0,\n        filieres: 0, utilisateurs: 0, enseignants: 0, classes: 0, niveaux: 0,\n        absences_aujourdhui: 0, retards_aujourdhui: 0, devoirs_en_cours: 0,\n        quiz_actifs: 0, factures_impayees: 0, diplomes_annee: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getActivityIcon = (type) => {\n    switch(type) {\n      case 'inscription': return '👤';\n      case 'note': return '📝';\n      case 'absence': return '⚠️';\n      case 'enseignant': return '👨‍🏫';\n      default: return '📢';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch(priority) {\n      case 'high': return '#dc3545';\n      case 'medium': return '#ffc107';\n      case 'low': return '#28a745';\n      default: return '#6c757d';\n    }\n  };\n\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh',\n    },\n    header: {\n      marginBottom: '30px',\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem',\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '50%',\n      color: 'white',\n    },\n    statContent: {\n      flex: 1,\n    },\n    statNumber: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px',\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit',\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px',\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px',\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff',\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem',\n    },\n    activityContent: {\n      flex: 1,\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px',\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem',\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid',\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n  };\n\n  const statCards = [\n    // Statistiques principales demandées\n    {\n      icon: FaBook,\n      number: stats.cours,\n      label: 'Cours',\n      color: '#007bff',\n      link: '/cours',\n      description: 'Total des cours disponibles'\n    },\n    {\n      icon: FaBookOpen,\n      number: stats.matieres,\n      label: 'Matières',\n      color: '#28a745',\n      link: '/matieres',\n      description: 'Matières enseignées'\n    },\n    {\n      icon: FaLayerGroup,\n      number: stats.groupes,\n      label: 'Groupes',\n      color: '#17a2b8',\n      link: '/groupes',\n      description: 'Groupes d\\'étudiants'\n    },\n    {\n      icon: FaGraduationCap,\n      number: stats.etudiants,\n      label: 'Étudiants',\n      color: '#6f42c1',\n      link: '/etudiants',\n      description: 'Étudiants inscrits'\n    },\n    {\n      icon: FaUserFriends,\n      number: stats.parents,\n      label: 'Parents',\n      color: '#fd7e14',\n      link: '/parents',\n      description: 'Parents d\\'étudiants'\n    },\n    {\n      icon: FaStream,\n      number: stats.filieres,\n      label: 'Filières',\n      color: '#e83e8c',\n      link: '/filieres',\n      description: 'Filières d\\'études'\n    },\n    {\n      icon: FaIdCard,\n      number: stats.utilisateurs,\n      label: 'Utilisateurs',\n      color: '#20c997',\n      link: '/utilisateurs',\n      description: 'Utilisateurs du système'\n    },\n    {\n      icon: FaChalkboardTeacher,\n      number: stats.enseignants,\n      label: 'Enseignants',\n      color: '#ffc107',\n      link: '/enseignants',\n      description: 'Professeurs actifs'\n    },\n  ];\n\n  // Statistiques secondaires pour le monitoring\n  const secondaryStats = [\n    {\n      icon: FaUsers,\n      number: stats.classes,\n      label: 'Classes',\n      color: '#6c757d',\n      link: '/classes'\n    },\n    {\n      icon: FaExclamationTriangle,\n      number: stats.absences_aujourdhui,\n      label: \"Absences Aujourd'hui\",\n      color: '#dc3545',\n      link: '/absences'\n    },\n    {\n      icon: FaClock,\n      number: stats.retards_aujourdhui,\n      label: \"Retards Aujourd'hui\",\n      color: '#fd7e14',\n      link: '/retards'\n    },\n    {\n      icon: FaClipboardList,\n      number: stats.devoirs_en_cours,\n      label: \"Devoirs en Cours\",\n      color: '#17a2b8',\n      link: '/devoirs'\n    },\n    {\n      icon: FaQuestionCircle,\n      number: stats.quiz_actifs,\n      label: \"Quiz Actifs\",\n      color: '#6f42c1',\n      link: '/quiz'\n    },\n    {\n      icon: FaFileInvoiceDollar,\n      number: stats.factures_impayees,\n      label: \"Factures Impayées\",\n      color: '#dc3545',\n      link: '/factures'\n    },\n    {\n      icon: FaCertificate,\n      number: stats.diplomes_annee,\n      label: \"Diplômes Cette Année\",\n      color: '#28a745',\n      link: '/diplomes'\n    },\n  ];\n\n  const quickActions = [\n    { icon: FaUsers, title: 'Gérer les Utilisateurs', description: 'Ajouter, modifier ou supprimer des utilisateurs', link: '/registers' },\n    { icon: FaChalkboardTeacher, title: 'Gérer les Cours', description: 'Planifier et organiser les cours', link: '/cours' },\n    { icon: FaGraduationCap, title: 'Gérer les Classes', description: 'Créer et organiser les classes', link: '/classes' },\n    { icon: FaCog, title: 'Configuration', description: 'Paramètres système et configuration', link: '/roles' },\n  ];\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête */}\n      <div style={styles.header}>\n        <h1 style={styles.welcomeText}>\n          <FaUserTie style={{ marginRight: '15px', color: '#007bff' }} />\n          Bienvenue, {user?.email || 'Responsable'}\n        </h1>\n        <p style={styles.subtitle}>Tableau de bord administrateur - Vue d'ensemble de l'école</p>\n      </div>\n\n      {/* Statistiques */}\n      <div style={styles.statsGrid}>\n        {statCards.map((stat, index) => (\n          <Link\n            key={index}\n            to={stat.link || '#'}\n            style={{ textDecoration: 'none', color: 'inherit' }}\n          >\n            <div \n              style={styles.statCard}\n              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{...styles.statIcon, backgroundColor: stat.color}}>\n                <stat.icon />\n              </div>\n              <div style={styles.statContent}>\n                <div style={styles.statNumber}>{stat.number}</div>\n                <div style={styles.statLabel}>{stat.label}</div>\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Actions rapides */}\n      <div style={styles.quickActionsGrid}>\n        {quickActions.map((action, index) => (\n          <Link\n            key={index}\n            to={action.link}\n            style={styles.actionCard}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n          >\n            <div style={styles.actionIcon}>\n              <action.icon />\n            </div>\n            <div style={styles.actionTitle}>{action.title}</div>\n            <div style={styles.actionDescription}>{action.description}</div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Contenu principal */}\n      <div style={styles.contentGrid}>\n        {/* Activités récentes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaChartBar style={styles.cardIcon} />\n            Activités Récentes\n          </h2>\n          {recentActivities.map(activity => (\n            <div key={activity.id} style={styles.activityItem}>\n              <span style={styles.activityIcon}>\n                {getActivityIcon(activity.type)}\n              </span>\n              <div style={styles.activityContent}>\n                <div style={styles.activityMessage}>{activity.message}</div>\n                <div style={styles.activityTime}>{activity.time}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Alertes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaExclamationTriangle style={styles.cardIcon} />\n            Alertes & Notifications\n          </h2>\n          {alertes.map(alerte => (\n            <div \n              key={alerte.id} \n              style={{\n                ...styles.alertItem,\n                borderLeftColor: getPriorityColor(alerte.priority),\n                backgroundColor: `${getPriorityColor(alerte.priority)}10`\n              }}\n            >\n              <div style={styles.alertMessage}>{alerte.message}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResponsableDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,EACxDC,aAAa,EAAEC,UAAU,EAAEC,KAAK,EAAEC,aAAa,EAAEC,qBAAqB,EACtEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAC7DC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EACjEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAC3C,gBAAgB;AAEvB,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAG5B,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC;IACjCiC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,mBAAmB,EAAE,CAAC;IACtBC,kBAAkB,EAAE,CAAC;IACrBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdwD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM1D,KAAK,CAAC2D,GAAG,CAAC,gEAAgE,EAAE;QACjGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,QAAQ,CAACI,IAAI,CAAC;MAEnD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBpC,QAAQ,CAAC+B,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAAC;;QAE7B;QACA,IAAIgC,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACsC,kBAAkB,EAAE;UAC1CnB,mBAAmB,CAACa,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACsC,kBAAkB,CAAC;QAC7D;;QAEA;QACA,MAAMC,UAAU,GAAG,EAAE;QAErB,IAAIP,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACgB,iBAAiB,GAAG,CAAC,EAAE;UAC7CuB,UAAU,CAACC,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,GAAGX,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACgB,iBAAiB,oCAAoC;YACrF4B,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAIZ,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACY,mBAAmB,GAAG,EAAE,EAAE;UAChD2B,UAAU,CAACC,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE,wCAAwCX,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACY,mBAAmB,EAAE;YAC1FgC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAIZ,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACa,kBAAkB,GAAG,CAAC,EAAE;UAC9C0B,UAAU,CAACC,IAAI,CAAC;YACdC,EAAE,EAAE,CAAC;YACLC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,GAAGX,QAAQ,CAACI,IAAI,CAACpC,KAAK,CAACa,kBAAkB,mCAAmC;YACrF+B,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEAL,UAAU,CAACC,IAAI,CAAC;UACdC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,uDAAuD;UAChEC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEFvB,UAAU,CAACkB,UAAU,CAAC;QAEtBZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD,CAAC,MAAM;QACL,MAAM,IAAIiB,KAAK,CAACb,QAAQ,CAACI,IAAI,CAACZ,KAAK,IAAI,4CAA4C,CAAC;MACtF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEC,QAAQ,CAAC,mEAAmE,CAAC;;MAE7E;MACAxB,QAAQ,CAAC;QACPC,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAC3DC,QAAQ,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QACpEC,mBAAmB,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,gBAAgB,EAAE,CAAC;QAClEC,WAAW,EAAE,CAAC;QAAEC,iBAAiB,EAAE,CAAC;QAAEC,cAAc,EAAE;MACxD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,eAAe,GAAIJ,IAAI,IAAK;IAChC,QAAOA,IAAI;MACT,KAAK,aAAa;QAAE,OAAO,IAAI;MAC/B,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,SAAS;QAAE,OAAO,IAAI;MAC3B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAIH,QAAQ,IAAK;IACrC,QAAOA,QAAQ;MACb,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMI,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,YAAY,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE;MACXC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDK,QAAQ,EAAE;MACRD,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDI,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDU,QAAQ,EAAE;MACRb,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCL,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,qBAAqB;MACjCC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACRd,QAAQ,EAAE,QAAQ;MAClBe,WAAW,EAAE,MAAM;MACnBrB,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,KAAK;MACnBP,KAAK,EAAE;IACT,CAAC;IACDc,WAAW,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVlB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDqB,SAAS,EAAE;MACTjB,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDoB,gBAAgB,EAAE;MAChBf,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDuB,UAAU,EAAE;MACV1B,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCY,SAAS,EAAE,QAAQ;MACnBV,UAAU,EAAE,qBAAqB;MACjCC,MAAM,EAAE,SAAS;MACjBU,cAAc,EAAE,MAAM;MACtBrB,KAAK,EAAE;IACT,CAAC;IACDsB,UAAU,EAAE;MACVxB,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAE,SAAS;MAChBJ,YAAY,EAAE;IAChB,CAAC;IACD2B,WAAW,EAAE;MACXzB,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACD4B,iBAAiB,EAAE;MACjBxB,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACD2B,WAAW,EAAE;MACXtB,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,SAAS;MAC9BC,GAAG,EAAE;IACP,CAAC;IACDqB,IAAI,EAAE;MACJjC,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE;IACb,CAAC;IACDmB,SAAS,EAAE;MACT7B,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE;IACd,CAAC;IACDmB,QAAQ,EAAE;MACRf,WAAW,EAAE,MAAM;MACnBb,KAAK,EAAE;IACT,CAAC;IACD6B,YAAY,EAAE;MACZrC,OAAO,EAAE,MAAM;MACfsC,YAAY,EAAE,gBAAgB;MAC9B3B,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE;IACd,CAAC;IACDsB,YAAY,EAAE;MACZlB,WAAW,EAAE,MAAM;MACnBf,QAAQ,EAAE;IACZ,CAAC;IACDkC,eAAe,EAAE;MACfjB,IAAI,EAAE;IACR,CAAC;IACDkB,eAAe,EAAE;MACfjC,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE,QAAQ;MAClBF,YAAY,EAAE;IAChB,CAAC;IACDsC,YAAY,EAAE;MACZlC,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDqC,SAAS,EAAE;MACT3C,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,KAAK;MACnBX,YAAY,EAAE,MAAM;MACpBwC,UAAU,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACZtC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EAED,MAAMsC,SAAS,GAAG;EAChB;EACA;IACEC,IAAI,EAAEjH,MAAM;IACZkH,MAAM,EAAElG,KAAK,CAACE,KAAK;IACnBiG,KAAK,EAAE,OAAO;IACdzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAEhH,UAAU;IAChBiH,MAAM,EAAElG,KAAK,CAACG,QAAQ;IACtBgG,KAAK,EAAE,UAAU;IACjBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE/G,YAAY;IAClBgH,MAAM,EAAElG,KAAK,CAACI,OAAO;IACrB+F,KAAK,EAAE,SAAS;IAChBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAEvH,eAAe;IACrBwH,MAAM,EAAElG,KAAK,CAACK,SAAS;IACvB8F,KAAK,EAAE,WAAW;IAClBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAEtH,aAAa;IACnBuH,MAAM,EAAElG,KAAK,CAACM,OAAO;IACrB6F,KAAK,EAAE,SAAS;IAChBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE5G,QAAQ;IACd6G,MAAM,EAAElG,KAAK,CAACO,QAAQ;IACtB4F,KAAK,EAAE,UAAU;IACjBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAE3G,QAAQ;IACd4G,MAAM,EAAElG,KAAK,CAACQ,YAAY;IAC1B2F,KAAK,EAAE,cAAc;IACrBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,IAAI,EAAExH,mBAAmB;IACzByH,MAAM,EAAElG,KAAK,CAACS,WAAW;IACzB0F,KAAK,EAAE,aAAa;IACpBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE;EACf,CAAC,CACF;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB;IACEL,IAAI,EAAEzH,OAAO;IACb0H,MAAM,EAAElG,KAAK,CAACU,OAAO;IACrByF,KAAK,EAAE,SAAS;IAChBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAElH,qBAAqB;IAC3BmH,MAAM,EAAElG,KAAK,CAACY,mBAAmB;IACjCuF,KAAK,EAAE,sBAAsB;IAC7BzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAExG,OAAO;IACbyG,MAAM,EAAElG,KAAK,CAACa,kBAAkB;IAChCsF,KAAK,EAAE,qBAAqB;IAC5BzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAErG,eAAe;IACrBsG,MAAM,EAAElG,KAAK,CAACc,gBAAgB;IAC9BqF,KAAK,EAAE,kBAAkB;IACzBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEpG,gBAAgB;IACtBqG,MAAM,EAAElG,KAAK,CAACe,WAAW;IACzBoF,KAAK,EAAE,aAAa;IACpBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEvG,mBAAmB;IACzBwG,MAAM,EAAElG,KAAK,CAACgB,iBAAiB;IAC/BmF,KAAK,EAAE,mBAAmB;IAC1BzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEtG,aAAa;IACnBuG,MAAM,EAAElG,KAAK,CAACiB,cAAc;IAC5BkF,KAAK,EAAE,sBAAsB;IAC7BzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMG,YAAY,GAAG,CACnB;IAAEN,IAAI,EAAEzH,OAAO;IAAEgI,KAAK,EAAE,wBAAwB;IAAEH,WAAW,EAAE,iDAAiD;IAAED,IAAI,EAAE;EAAa,CAAC,EACtI;IAAEH,IAAI,EAAExH,mBAAmB;IAAE+H,KAAK,EAAE,iBAAiB;IAAEH,WAAW,EAAE,kCAAkC;IAAED,IAAI,EAAE;EAAS,CAAC,EACxH;IAAEH,IAAI,EAAEvH,eAAe;IAAE8H,KAAK,EAAE,mBAAmB;IAAEH,WAAW,EAAE,gCAAgC;IAAED,IAAI,EAAE;EAAW,CAAC,EACtH;IAAEH,IAAI,EAAEpH,KAAK;IAAE2H,KAAK,EAAE,eAAe;IAAEH,WAAW,EAAE,qCAAqC;IAAED,IAAI,EAAE;EAAS,CAAC,CAC5G;EAED,oBACEpI,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACC,SAAU;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACK,MAAO;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBhJ,KAAA,CAAAyI,aAAA;IAAIC,KAAK,EAAE1D,MAAM,CAACO,WAAY;IAAAoD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BhJ,KAAA,CAAAyI,aAAA,CAAClI,SAAS;IAACmI,KAAK,EAAE;MAAEnC,WAAW,EAAE,MAAM;MAAEb,KAAK,EAAE;IAAU,CAAE;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACpD,EAAC,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkH,KAAK,KAAI,aACzB,CAAC,eACLjJ,KAAA,CAAAyI,aAAA;IAAGC,KAAK,EAAE1D,MAAM,CAACW,QAAS;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+DAA6D,CACrF,CAAC,eAGNhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACY,SAAU;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BhB,SAAS,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBpJ,KAAA,CAAAyI,aAAA,CAACpI,IAAI;IACHgJ,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEH,IAAI,CAACf,IAAI,IAAI,GAAI;IACrBM,KAAK,EAAE;MAAE3B,cAAc,EAAE,MAAM;MAAErB,KAAK,EAAE;IAAU,CAAE;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpDhJ,KAAA,CAAAyI,aAAA;IACEC,KAAK,EAAE1D,MAAM,CAACgB,QAAS;IACvBuD,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,eAAgB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvEhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAG1D,MAAM,CAACsB,QAAQ;MAAEnB,eAAe,EAAEgE,IAAI,CAACzD;IAAK,CAAE;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5DhJ,KAAA,CAAAyI,aAAA,CAACU,IAAI,CAAClB,IAAI;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACNhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACwB,WAAY;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC0B,UAAW;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEG,IAAI,CAACjB,MAAY,CAAC,eAClDlI,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC2B,SAAU;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEG,IAAI,CAAChB,KAAW,CAC5C,CACF,CACD,CACP,CACE,CAAC,eAGNnI,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC4B,gBAAiB;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCT,YAAY,CAACW,GAAG,CAAC,CAACU,MAAM,EAAER,KAAK,kBAC9BpJ,KAAA,CAAAyI,aAAA,CAACpI,IAAI;IACHgJ,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEM,MAAM,CAACxB,IAAK;IAChBM,KAAK,EAAE1D,MAAM,CAAC6B,UAAW;IACzB0C,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,eAAgB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvEhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACgC,UAAW;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BhJ,KAAA,CAAAyI,aAAA,CAACmB,MAAM,CAAC3B,IAAI;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACX,CAAC,eACNhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACiC,WAAY;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEY,MAAM,CAACpB,KAAW,CAAC,eACpDxI,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACkC,iBAAkB;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEY,MAAM,CAACvB,WAAiB,CAC3D,CACP,CACE,CAAC,eAGNrI,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACmC,WAAY;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7BhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACoC,IAAK;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBhJ,KAAA,CAAAyI,aAAA;IAAIC,KAAK,EAAE1D,MAAM,CAACqC,SAAU;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BhJ,KAAA,CAAAyI,aAAA,CAAC7H,UAAU;IAAC8H,KAAK,EAAE1D,MAAM,CAACsC,QAAS;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAEpC,CAAC,EACJ9F,gBAAgB,CAACgG,GAAG,CAACW,QAAQ,iBAC5B7J,KAAA,CAAAyI,aAAA;IAAKY,GAAG,EAAEQ,QAAQ,CAACpF,EAAG;IAACiE,KAAK,EAAE1D,MAAM,CAACuC,YAAa;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChDhJ,KAAA,CAAAyI,aAAA;IAAMC,KAAK,EAAE1D,MAAM,CAACyC,YAAa;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BlE,eAAe,CAAC+E,QAAQ,CAACnF,IAAI,CAC1B,CAAC,eACP1E,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC0C,eAAgB;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjChJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC2C,eAAgB;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEa,QAAQ,CAAClF,OAAa,CAAC,eAC5D3E,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC4C,YAAa;IAAAe,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEa,QAAQ,CAACC,IAAU,CAClD,CACF,CACN,CACE,CAAC,eAGN9J,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAACoC,IAAK;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBhJ,KAAA,CAAAyI,aAAA;IAAIC,KAAK,EAAE1D,MAAM,CAACqC,SAAU;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BhJ,KAAA,CAAAyI,aAAA,CAAC1H,qBAAqB;IAAC2H,KAAK,EAAE1D,MAAM,CAACsC,QAAS;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,2BAE/C,CAAC,EACJ5F,OAAO,CAAC8F,GAAG,CAACa,MAAM,iBACjB/J,KAAA,CAAAyI,aAAA;IACEY,GAAG,EAAEU,MAAM,CAACtF,EAAG;IACfiE,KAAK,EAAE;MACL,GAAG1D,MAAM,CAAC6C,SAAS;MACnBmC,eAAe,EAAEjF,gBAAgB,CAACgF,MAAM,CAACnF,QAAQ,CAAC;MAClDO,eAAe,EAAE,GAAGJ,gBAAgB,CAACgF,MAAM,CAACnF,QAAQ,CAAC;IACvD,CAAE;IAAA+D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFhJ,KAAA,CAAAyI,aAAA;IAAKC,KAAK,EAAE1D,MAAM,CAAC+C,YAAa;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEe,MAAM,CAACpF,OAAa,CACnD,CACN,CACE,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAe7C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}