import React, { useState, useContext } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  FaBars, 
  FaTimes, 
  FaBook, 
  FaChalkboardTeacher, 
  FaUserFriends, 
  FaClipboardList, 
  FaTasks, 
  FaChartBar, 
  FaCalendarTimes, 
  FaClock,
  FaCalendarAlt,
  FaQuestionCircle,
  FaHome,
  FaEnvelope
} from 'react-icons/fa';
import { AuthContext } from '../context/AuthContext';

const NavbarTeacher = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const location = useLocation();
  const { user } = useContext(AuthContext);

  const isActive = (path) => {
    return location.pathname === path;
  };

  // Menu items spécifiques aux enseignants - pages pertinentes uniquement
  const teacherMenuItems = [
    { path: '/dashboard/enseignant', label: '<PERSON>au de Bord', icon: <FaHome /> },
    { path: '/matieres', label: 'Matières', icon: <FaBook /> },
    { path: '/classes', label: 'Classes', icon: <FaChalkboardTeacher /> },
    { path: '/groupes', label: 'Groupes', icon: <FaUserFriends /> },
    { path: '/emplois-du-temps', label: 'Emplois du Temps', icon: <FaCalendarAlt /> },
    { path: '/cours', label: 'Cours', icon: <FaClipboardList /> },
    { path: '/devoirs', label: 'Devoirs', icon: <FaTasks /> },
    { path: '/quiz', label: 'Quiz', icon: <FaQuestionCircle /> },
    { path: '/reponses-quiz', label: 'Réponses Quiz', icon: <FaQuestionCircle /> },
    { path: '/notes', label: 'Notes', icon: <FaChartBar /> },
    { path: '/absences', label: 'Absences', icon: <FaCalendarTimes /> },
    { path: '/retards', label: 'Retards', icon: <FaClock /> },
    { path: '/etudiants', label: 'Étudiants', icon: <FaUserFriends /> },
    { path: '/messagerie', label: 'Messagerie', icon: <FaEnvelope /> }
  ];

  const navStyles = {
    nav: {
      position: 'fixed',
      left: 0,
      top: 0,
      height: '100vh',
      width: isExpanded ? '280px' : '80px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      transition: 'width 0.3s ease',
      zIndex: 1000,
      boxShadow: '2px 0 10px rgba(0,0,0,0.1)',
      overflow: 'hidden'
    },
    toggleButton: {
      position: 'absolute',
      top: '20px',
      right: '20px',
      background: 'transparent',
      border: 'none',
      color: 'white',
      fontSize: '20px',
      cursor: 'pointer',
      padding: '10px',
      borderRadius: '50%',
      transition: 'all 0.3s ease',
      zIndex: 1001
    },
    menuContainer: {
      marginTop: '80px',
      padding: '0 10px'
    },
    menuItem: {
      display: 'flex',
      alignItems: 'center',
      padding: '15px 20px',
      margin: '5px 0',
      color: 'white',
      textDecoration: 'none',
      borderRadius: '12px',
      transition: 'all 0.3s ease',
      position: 'relative',
      overflow: 'hidden'
    },
    menuItemActive: {
      backgroundColor: 'rgba(255,255,255,0.2)',
      transform: 'translateX(5px)',
      boxShadow: '0 4px 15px rgba(0,0,0,0.2)'
    },
    menuIcon: {
      fontSize: '20px',
      minWidth: '20px',
      textAlign: 'center'
    },
    menuLabel: {
      marginLeft: '20px',
      fontSize: '14px',
      fontWeight: '500',
      opacity: isExpanded ? 1 : 0,
      transition: 'opacity 0.3s ease',
      whiteSpace: 'nowrap'
    },
    teacherIndicator: {
      fontSize: '0.7rem',
      opacity: 0.7,
      marginLeft: 'auto',
      opacity: isExpanded ? 0.7 : 0,
      transition: 'opacity 0.3s ease'
    }
  };

  return (
    <nav style={navStyles.nav}>
      {/* Bouton toggle */}
      <button
        style={navStyles.toggleButton}
        onClick={() => setIsExpanded(!isExpanded)}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = 'transparent';
          e.target.style.transform = 'scale(1)';
        }}
      >
        {isExpanded ? <FaTimes /> : <FaBars />}
      </button>

      {/* Menu items */}
      <div style={navStyles.menuContainer}>
        {teacherMenuItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            style={{
              ...navStyles.menuItem,
              ...(isActive(item.path) ? navStyles.menuItemActive : {})
            }}
            onMouseEnter={(e) => {
              if (!isActive(item.path)) {
                e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
                e.target.style.transform = 'translateX(3px)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isActive(item.path)) {
                e.target.style.backgroundColor = 'transparent';
                e.target.style.transform = 'translateX(0)';
              }
            }}
          >
            <span style={navStyles.menuIcon}>
              {item.icon}
            </span>
            <span style={navStyles.menuLabel}>
              {item.label}
            </span>
            <span style={navStyles.teacherIndicator}>
              👨‍🏫
            </span>
          </Link>
        ))}
      </div>

      {/* Indicateur de rôle en bas */}
      {isExpanded && (
        <div style={{
          position: 'absolute',
          bottom: '20px',
          left: '20px',
          right: '20px',
          padding: '15px',
          backgroundColor: 'rgba(255,255,255,0.1)',
          borderRadius: '10px',
          color: 'white',
          fontSize: '12px',
          textAlign: 'center'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
            👨‍🏫 Interface Enseignant
          </div>
          <div style={{ opacity: 0.8 }}>
            {user?.nom || user?.email || 'Enseignant'}
          </div>
        </div>
      )}
    </nav>
  );
};

export default NavbarTeacher;
