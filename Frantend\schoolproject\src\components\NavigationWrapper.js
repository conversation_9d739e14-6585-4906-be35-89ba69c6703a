import React, { useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import Navbar from './Navbar';
import NavbarStudent from './NavbarStudent';
import NavbarTop from './NavbarTop';

const NavigationWrapper = ({ children }) => {
  const { user, isAuthenticated } = useContext(AuthContext);

  // Si l'utilisateur n'est pas connecté, pas de navigation
  if (!isAuthenticated) {
    return children;
  }

  // Déterminer quelle navbar utiliser selon le rôle
  const renderNavbar = () => {
    const userRole = user?.role?.toLowerCase();
    
    // Pour les étudiants, utiliser la navbar spécialisée
    if (userRole === 'etudiant' || userRole === 'élève') {
      return <NavbarStudent />;
    }
    
    // Pour tous les autres rôles, utiliser la navbar standard
    return <Navbar />;
  };

  return (
    <div>
      {renderNavbar()}
      <NavbarTop />
      <div style={{
        marginLeft: '110px',
        marginTop: '60px',
        padding: '20px'
      }}>
        {children}
      </div>
    </div>
  );
};

export default NavigationWrapper;
