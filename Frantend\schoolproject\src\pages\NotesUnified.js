import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import { AuthContext } from '../context/AuthContext';
import {
    filterNotes,
    canManageData,
    isStudent,
    logSecurityEvent
} from '../utils/studentDataFilter';
import '../css/Factures.css';

const NotesUnified = () => {
    const { user } = useContext(AuthContext);
    const [notes, setNotes] = useState([]);
    const [devoirsDisponibles, setDevoirsDisponibles] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [showGenerateModal, setShowGenerateModal] = useState(false);
    const [editingNote, setEditingNote] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterMatiere, setFilterMatiere] = useState('all');
    const [filterEtudiant, setFilterEtudiant] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [statistics, setStatistics] = useState({});
    const [matieres, setMatieres] = useState([]);
    const [etudiants, setEtudiants] = useState([]);
    const [formData, setFormData] = useState({
        etudiant_id: '',
        devoir_id: '',
        matiere_id: '',
        note: ''
    });

    // Déterminer le rôle et les permissions avec notre système unifié
    const isEtudiant = isStudent(user);
    const isEnseignant = user?.role === 'enseignant' || user?.role === 'Enseignant';
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';

    // Permissions selon les spécifications
    const canManage = canManageData(user); // Admin et enseignants peuvent CRUD les notes
    const canView = isEtudiant || isEnseignant || isAdmin; // Tous peuvent voir (avec restrictions)

    useEffect(() => {
        if (canView) {
            fetchNotes();
            if (isEnseignant) {
                fetchDevoirsDisponibles();
                fetchEtudiants();
                fetchMatieres();
            }
        }
    }, [canView, isEnseignant]);

    const fetchNotes = async () => {
        try {
            console.log('🔄 Chargement des notes...');

            // Déterminer le token selon le rôle
            let authToken = 'default-token';
            if (isEtudiant) authToken = 'etudiant-token';
            else if (isEnseignant) authToken = 'enseignant-token';
            else if (isAdmin) authToken = 'admin-token';

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/api.php', {
                headers: { Authorization: `Bearer ${authToken}` }
            });

            console.log('✅ Réponse API notes:', response.data);
            if (response.data.success) {
                const notesData = response.data.data || [];
                setNotes(notesData);
                
                // Calculer les statistiques
                const stats = calculateStatistics(notesData);
                setStatistics(stats);
                
                // Extraire les matières et étudiants uniques pour les filtres (seulement si pas déjà chargés)
                if (!isEtudiant && !isEnseignant) {
                    // Pour les admins, utiliser les noms des notes pour les filtres uniquement
                    if (isAdmin && matieres.length === 0) {
                        const matieresUniques = [...new Set(notesData.map(n => n.matiere_nom).filter(Boolean))];
                        setMatieres(matieresUniques);
                    }

                    if (isAdmin && etudiants.length === 0) {
                        const etudiantsUniques = [...new Set(notesData.map(n => n.etudiant_nom).filter(Boolean))];
                        setEtudiants(etudiantsUniques);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des notes:', error);
            if (error.response?.status === 403) {
                Swal.fire('Accès refusé', 'Vous n\'avez pas les permissions pour consulter les notes', 'error');
            } else {
                Swal.fire('Erreur', 'Impossible de charger les notes', 'error');
            }
            setNotes([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            console.log('🔄 Chargement des étudiants...');

            // Utiliser l'authentification réelle
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);

            if (response.data.success) {
                setEtudiants(response.data.etudiants);
                console.log('✅ Étudiants chargés:', response.data.etudiants.length);
                console.log('📚 Premier étudiant:', response.data.etudiants[0]);
            } else {
                console.error('❌ Erreur API étudiants:', response.data.error);
                setEtudiants([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des étudiants:', error);
            console.error('❌ Détails erreur:', error.response?.data || error.message);
            setEtudiants([]);
        }
    };

    const fetchMatieres = async () => {
        try {
            console.log('🔄 Chargement des matières...');

            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('🔍 DEBUG MATIERES API Response:', response.data);

            if (response.data.success) {
                setMatieres(response.data.matieres || response.data);
                console.log('✅ Matières chargées:', response.data.matieres?.length || response.data.length);
            } else {
                console.error('❌ Erreur API matières:', response.data.error);
                setMatieres([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des matières:', error);
            console.error('❌ Détails erreur:', error.response?.data || error.message);
            setMatieres([]);
        }
    };

    const fetchDevoirsDisponibles = async () => {
        try {
            console.log('🔄 Chargement des devoirs disponibles...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/notes/devoirs-disponibles.php', {
                headers: { Authorization: `Bearer enseignant-token` }
            });

            console.log('✅ Réponse API devoirs disponibles:', response.data);
            if (response.data.success) {
                setDevoirsDisponibles(response.data.data || []);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des devoirs:', error);
            setDevoirsDisponibles([]);
        }
    };

    const calculateStatistics = (notesData) => {
        const total = notesData.length;
        const moyenne_generale = total > 0 ? 
            notesData.reduce((sum, n) => sum + parseFloat(n.note || 0), 0) / total : 0;
        
        const notes_excellentes = notesData.filter(n => parseFloat(n.note) >= 16).length;
        const notes_bonnes = notesData.filter(n => parseFloat(n.note) >= 12 && parseFloat(n.note) < 16).length;
        const notes_moyennes = notesData.filter(n => parseFloat(n.note) >= 10 && parseFloat(n.note) < 12).length;
        const notes_faibles = notesData.filter(n => parseFloat(n.note) < 10).length;
        
        const etudiants = [...new Set(notesData.map(n => n.etudiant_id).filter(Boolean))];
        const matieres = [...new Set(notesData.map(n => n.matiere_id).filter(Boolean))];
        const devoirs = [...new Set(notesData.map(n => n.devoir_id).filter(Boolean))];
        
        return {
            total_notes: total,
            moyenne_generale: Math.round(moyenne_generale * 100) / 100,
            notes_excellentes,
            notes_bonnes,
            notes_moyennes,
            notes_faibles,
            nombre_etudiants: etudiants.length,
            nombre_matieres: matieres.length,
            nombre_devoirs: devoirs.length
        };
    };

    const handleGenerateNotes = async (devoir_id) => {
        const result = await Swal.fire({
            title: 'Générer les notes automatiquement ?',
            text: 'Les notes seront calculées à partir des réponses aux quiz de ce devoir.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Oui, générer',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                console.log('🔄 Génération automatique des notes pour le devoir:', devoir_id);
                
                const response = await axios.post('http://localhost/Project_PFE/Backend/pages/notes/api.php', {
                    action: 'generate',
                    devoir_id: devoir_id
                }, {
                    headers: { 
                        Authorization: `Bearer enseignant-token`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('✅ Notes générées:', response.data);

                if (response.data.success) {
                    const details = response.data.details;
                    Swal.fire({
                        title: 'Notes générées !',
                        html: `
                            <p><strong>Nouvelles notes :</strong> ${details.notes_generees}</p>
                            <p><strong>Notes mises à jour :</strong> ${details.notes_mises_a_jour}</p>
                            <p><strong>Total étudiants :</strong> ${details.total_etudiants}</p>
                        `,
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    });
                    fetchNotes();
                    fetchDevoirsDisponibles();
                } else {
                    Swal.fire('Erreur', response.data.error || 'Erreur lors de la génération', 'error');
                }
            } catch (error) {
                console.error('❌ Erreur génération:', error);
                Swal.fire('Erreur', error.response?.data?.error || 'Erreur lors de la génération des notes', 'error');
            }
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les enseignants peuvent créer/modifier des notes', 'error');
            return;
        }

        if (!formData.etudiant_id || !formData.devoir_id || !formData.matiere_id) {
            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');
            return;
        }

        try {
            const url = 'http://localhost/Project_PFE/Backend/pages/notes/api.php';
            const method = editingNote ? 'PUT' : 'POST';
            const data = editingNote ? { ...formData, id: editingNote.id } : formData;

            console.log('🔄 Envoi note:', { method, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer enseignant-token`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Note envoyée:', response.data);

            if (response.data.success) {
                Swal.fire({
                    title: 'Succès !',
                    text: response.data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
                setShowModal(false);
                setEditingNote(null);
                resetForm();
                fetchNotes();
                if (isEnseignant) fetchDevoirsDisponibles();
            } else {
                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (note) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les enseignants peuvent modifier les notes', 'error');
            return;
        }

        setEditingNote(note);
        setFormData({
            etudiant_id: note.etudiant_id || '',
            devoir_id: note.devoir_id || '',
            matiere_id: note.matiere_id || '',
            note: note.note || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les enseignants peuvent supprimer les notes', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Supprimer cette note ?',
            text: 'Cette action est irréversible !',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                console.log('🗑️ Suppression note ID:', id);
                
                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/notes/api.php', {
                    headers: { 
                        Authorization: `Bearer enseignant-token`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                console.log('✅ Note supprimée:', response.data);

                if (response.data.success) {
                    Swal.fire({
                        title: 'Supprimé !',
                        text: response.data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    fetchNotes();
                    if (isEnseignant) fetchDevoirsDisponibles();
                } else {
                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer la note', 'error');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer la note', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            devoir_id: '',
            matiere_id: '',
            note: ''
        });
    };

    // ÉTAPE 1 : Filtrage de sécurité - les étudiants ne voient que leurs propres notes
    const securityFilteredNotes = filterNotes(notes, user);

    // Log de sécurité si des données ont été filtrées
    if (isStudent(user) && securityFilteredNotes.length !== notes.length) {
        logSecurityEvent('NOTE_ACCESS_FILTERED', user, {
            total: notes.length,
            filtered: securityFilteredNotes.length
        });
    }

    // ÉTAPE 2 : Filtrage par recherche et critères
    const filteredNotes = securityFilteredNotes.filter(note => {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = (
            (note.etudiant_nom || '').toLowerCase().includes(searchLower) ||
            (note.devoir_titre || '').toLowerCase().includes(searchLower) ||
            (note.matiere_nom || '').toLowerCase().includes(searchLower) ||
            (note.note || '').toString().includes(searchLower)
        );

        const matchesMatiere = filterMatiere === 'all' || note.matiere_nom === filterMatiere;
        const matchesEtudiant = filterEtudiant === 'all' || note.etudiant_nom === filterEtudiant;

        return matchesSearch && matchesMatiere && matchesEtudiant;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentNotes = filteredNotes.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, filterMatiere, filterEtudiant]);

    const getNoteBadge = (note) => {
        const noteValue = parseFloat(note);
        let style = {
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '0.9em',
            fontWeight: 'bold'
        };

        if (noteValue >= 16) {
            style = {...style, backgroundColor: '#28a745', color: 'white'};
            return <span style={style}>🏆 {note}/20</span>;
        } else if (noteValue >= 12) {
            style = {...style, backgroundColor: '#17a2b8', color: 'white'};
            return <span style={style}>👍 {note}/20</span>;
        } else if (noteValue >= 10) {
            style = {...style, backgroundColor: '#ffc107', color: 'black'};
            return <span style={style}>📊 {note}/20</span>;
        } else {
            style = {...style, backgroundColor: '#dc3545', color: 'white'};
            return <span style={style}>📉 {note}/20</span>;
        }
    };

    const getHeaderTitle = () => {
        if (isEtudiant) return '📊 Mes Notes';
        if (isEnseignant) return '👨‍🏫 Gestion des Notes';
        if (isAdmin) return '🛡️ Administration - Notes';
        return '📊 Notes';
    };

    const getInfoMessage = () => {
        if (isEtudiant) {
            return {
                style: { backgroundColor: '#fff3cd', border: '1px solid #ffc107', color: '#856404' },
                text: '📊 Vous consultez vos notes. Les notes sont calculées automatiquement à partir de vos réponses aux quiz.'
            };
        } else if (isEnseignant) {
            return {
                style: { backgroundColor: '#d4edda', border: '1px solid #c3e6cb', color: '#155724' },
                text: '👨‍🏫 Mode Enseignant : Vous pouvez créer, modifier et supprimer les notes. Utilisez la génération automatique pour calculer les notes à partir des quiz.'
            };
        } else if (isAdmin) {
            return {
                style: { backgroundColor: '#e2e3e5', border: '1px solid #d6d8db', color: '#383d41' },
                text: '🛡️ Mode Administrateur : Vous consultez toutes les notes en lecture seule. Aucune modification n\'est possible.'
            };
        }
        return null;
    };

    const styles = {
        accessDenied: {
            textAlign: 'center',
            padding: '50px 20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            margin: '20px 0'
        },
        idBadge: {
            backgroundColor: isAdmin ? '#6f42c1' : '#007bff',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '0.8em',
            fontWeight: 'bold'
        }
    };

    // Vérification d'accès
    if (!canView) {
        return (
            <div className="factures-container">
                <div style={styles.accessDenied}>
                    <h2>🚫 Accès Refusé</h2>
                    <p>Vous n'avez pas les permissions pour accéder aux notes.</p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des notes...</p>
            </div>
        );
    }

    const infoMessage = getInfoMessage();

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>{getHeaderTitle()}</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredNotes.length} note(s) trouvée(s)
                    </span>
                    {canManage && (
                        <div>
                            <button 
                                className="btn btn-success"
                                onClick={() => setShowGenerateModal(true)}
                                style={{ marginRight: '10px' }}
                            >
                                <img src="/auto.png" alt="Générer" /> Génération Auto
                            </button>
                            <button 
                                className="btn btn-primary"
                                onClick={() => setShowModal(true)}
                            >
                                <img src="/plus.png" alt="Ajouter" /> Nouvelle Note
                            </button>
                        </div>
                    )}
                </div>
            </div>

            {/* Message d'information selon le rôle */}
            {infoMessage && (
                <div style={{
                    ...infoMessage.style,
                    borderRadius: '8px',
                    padding: '15px',
                    margin: '20px 0'
                }}>
                    <p style={{ margin: '0' }}>{infoMessage.text}</p>
                </div>
            )}

            {/* Statistiques selon le rôle */}
            {statistics.total_notes > 0 && (
                <div style={{
                    display: 'grid',
                    gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(120px, 1fr))' : 'repeat(auto-fit, minmax(150px, 1fr))',
                    gap: '15px',
                    backgroundColor: isAdmin ? '#f8f9fa' : isEtudiant ? '#fff3cd' : '#d4edda',
                    border: `1px solid ${isAdmin ? '#dee2e6' : isEtudiant ? '#ffc107' : '#c3e6cb'}`,
                    borderRadius: '8px',
                    padding: '20px',
                    margin: '20px 0'
                }}>
                    <div style={{
                        textAlign: 'center',
                        padding: '15px',
                        backgroundColor: 'white',
                        borderRadius: '6px',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: isAdmin ? '#6f42c1' : '#007bff' }}>
                            {statistics.total_notes}
                        </div>
                        <div style={{ fontSize: '12px', color: '#6c757d' }}>
                            {isEtudiant ? 'Mes Notes' : 'Total Notes'}
                        </div>
                    </div>

                    <div style={{
                        textAlign: 'center',
                        padding: '15px',
                        backgroundColor: 'white',
                        borderRadius: '6px',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }}>
                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#17a2b8' }}>
                            {statistics.moyenne_generale}
                        </div>
                        <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyenne</div>
                    </div>

                    {!isEtudiant && (
                        <>
                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>
                                    {statistics.notes_excellentes}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Excellentes (≥16)</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffc107' }}>
                                    {statistics.notes_moyennes}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Moyennes (10-12)</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545' }}>
                                    {statistics.notes_faibles}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Faibles (&lt;10)</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fd7e14' }}>
                                    {statistics.nombre_etudiants}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Étudiants</div>
                            </div>

                            <div style={{
                                textAlign: 'center',
                                padding: '15px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}>
                                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#20c997' }}>
                                    {statistics.nombre_devoirs}
                                </div>
                                <div style={{ fontSize: '12px', color: '#6c757d' }}>Devoirs</div>
                            </div>
                        </>
                    )}
                </div>
            )}

            {/* Filtres et recherche */}
            <div className="search-section">
                <div className="search-bar">
                    <img src="/search.png" alt="Rechercher" />
                    <input
                        type="text"
                        placeholder={isEtudiant ? "Rechercher dans vos notes..." : "Rechercher par étudiant, devoir, matière, note..."}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                {!isEtudiant && (
                    <div className="filter-section" style={{
                        display: 'grid',
                        gridTemplateColumns: isAdmin ? 'repeat(auto-fit, minmax(200px, 1fr))' : 'repeat(auto-fit, minmax(250px, 1fr))',
                        gap: '10px',
                        marginTop: '15px'
                    }}>
                        <select
                            value={filterMatiere}
                            onChange={(e) => setFilterMatiere(e.target.value)}
                            className="filter-select"
                        >
                            <option value="all">📖 Toutes les matières</option>
                            {matieres.map(matiere => (
                                <option key={matiere} value={matiere}>{matiere}</option>
                            ))}
                        </select>

                        {isAdmin && (
                            <select
                                value={filterEtudiant}
                                onChange={(e) => setFilterEtudiant(e.target.value)}
                                className="filter-select"
                            >
                                <option value="all">👤 Tous les étudiants</option>
                                {etudiants.map(etudiant => (
                                    <option key={etudiant} value={etudiant}>{etudiant}</option>
                                ))}
                            </select>
                        )}
                    </div>
                )}
            </div>

            {/* Tableau des notes */}
            <div className="table-container">
                {filteredNotes.length === 0 ? (
                    <div className="no-data">
                        <img src="/empty.png" alt="Aucune donnée" />
                        <p>Aucune note trouvée</p>
                        <p>{isEtudiant ? "Aucune note n'a encore été attribuée" : "Modifiez vos critères de recherche ou filtres"}</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    {!isEtudiant && <th>👤 Étudiant</th>}
                                    <th>📚 Devoir</th>
                                    <th>📖 Matière</th>
                                    <th>📊 Note</th>
                                    <th>📅 Date</th>
                                    {isAdmin && <th>🏫 Classe</th>}
                                    {canManage && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentNotes.map((note) => (
                                    <tr key={note.id}>
                                        <td>
                                            <span style={styles.idBadge}>
                                                #{note.id}
                                            </span>
                                        </td>
                                        {!isEtudiant && (
                                            <td>
                                                <div className="user-info">
                                                    <strong style={{ fontSize: '0.9em' }}>
                                                        {note.etudiant_nom || 'Nom non disponible'}
                                                    </strong>
                                                    <br />
                                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>
                                                        {note.etudiant_email || 'Email non disponible'}
                                                    </small>
                                                </div>
                                            </td>
                                        )}
                                        <td>
                                            <div style={{ maxWidth: '200px' }}>
                                                <strong style={{ fontSize: '0.9em' }}>
                                                    {note.devoir_titre || 'Devoir non spécifié'}
                                                </strong>
                                                {note.date_remise && (
                                                    <div style={{ fontSize: '0.8em', color: '#6c757d' }}>
                                                        Remise: {note.date_remise}
                                                    </div>
                                                )}
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3e0',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {note.matiere_nom || 'Non spécifiée'}
                                            </span>
                                        </td>
                                        <td>
                                            {getNoteBadge(note.note)}
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em' }}>
                                                {note.date_formatted || note.date_enregistrement}
                                            </span>
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <span style={{
                                                    padding: '3px 6px',
                                                    backgroundColor: '#f3e5f5',
                                                    borderRadius: '4px',
                                                    fontSize: '0.8em'
                                                }}>
                                                    {note.classe_nom || 'Non spécifiée'}
                                                </span>
                                            </td>
                                        )}
                                        {canManage && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(note)}
                                                        title="Modifier la note"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(note.id)}
                                                        title="Supprimer la note"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination">
                    <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="btn btn-outline-primary"
                    >
                        Précédent
                    </button>

                    <div className="page-info">
                        Page {currentPage} sur {totalPages}
                    </div>

                    <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="btn btn-outline-primary"
                    >
                        Suivant
                    </button>
                </div>
            )}

            {/* Modal de génération automatique (enseignants uniquement) */}
            {showGenerateModal && canManage && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>🤖 Génération Automatique des Notes</h3>
                            <button
                                className="close-btn"
                                onClick={() => setShowGenerateModal(false)}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <div style={{ padding: '20px' }}>
                            <p style={{ marginBottom: '20px', color: '#6c757d' }}>
                                Sélectionnez un devoir pour générer automatiquement les notes à partir des réponses aux quiz.
                            </p>

                            {devoirsDisponibles.length === 0 ? (
                                <div style={{ textAlign: 'center', padding: '20px' }}>
                                    <p>Aucun devoir disponible pour la génération de notes.</p>
                                </div>
                            ) : (
                                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                                    {devoirsDisponibles.map(devoir => (
                                        <div key={devoir.id} style={{
                                            border: '1px solid #dee2e6',
                                            borderRadius: '8px',
                                            padding: '15px',
                                            marginBottom: '10px',
                                            backgroundColor: devoir.peut_generer_notes ? '#f8f9fa' : '#fff3cd'
                                        }}>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <div>
                                                    <h5 style={{ margin: '0 0 5px 0' }}>{devoir.titre}</h5>
                                                    <p style={{ margin: '0', fontSize: '0.9em', color: '#6c757d' }}>
                                                        {devoir.matiere_nom} - {devoir.classe_nom}
                                                    </p>
                                                    <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px' }}>
                                                        📊 {devoir.nombre_quiz} quiz • 👥 {devoir.nombre_etudiants_repondus} réponses • 📝 {devoir.notes_existantes} notes
                                                    </div>
                                                    <div style={{ fontSize: '0.8em', marginTop: '5px' }}>
                                                        <span style={{
                                                            padding: '2px 6px',
                                                            borderRadius: '4px',
                                                            backgroundColor: devoir.peut_generer_notes ? '#d4edda' : '#fff3cd',
                                                            color: devoir.peut_generer_notes ? '#155724' : '#856404'
                                                        }}>
                                                            {devoir.statut}
                                                        </span>
                                                    </div>
                                                </div>
                                                <button
                                                    className="btn btn-success"
                                                    onClick={() => {
                                                        setShowGenerateModal(false);
                                                        handleGenerateNotes(devoir.id);
                                                    }}
                                                    disabled={!devoir.peut_generer_notes}
                                                    title={devoir.peut_generer_notes ? 'Générer les notes' : 'Génération impossible'}
                                                >
                                                    🚀 Générer
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier une note (enseignants uniquement) */}
            {showModal && canManage && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingNote ? 'Modifier la note' : 'Nouvelle note'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingNote(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant *</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                    disabled={editingNote}
                                    style={{
                                        borderColor: formData.etudiant_id ? '#28a745' : '#ced4da',
                                        borderWidth: '2px',
                                        borderStyle: 'solid'
                                    }}
                                >
                                    <option value="">Sélectionner un étudiant...</option>
                                    {etudiants.length > 0 ? (
                                        etudiants.map(etudiant => (
                                            <option key={etudiant.id || etudiant.etudiant_id} value={etudiant.id || etudiant.etudiant_id}>
                                                📚 {etudiant.nom || etudiant.nom_prenom} | Groupe: {etudiant.groupe_nom || 'N/A'} | Classe: {etudiant.classe_nom || 'N/A'}
                                            </option>
                                        ))
                                    ) : (
                                        <option value="" disabled>Chargement des étudiants...</option>
                                    )}
                                </select>
                            </div>

                            <div className="form-group">
                                <label>Devoir *</label>
                                <select
                                    value={formData.devoir_id}
                                    onChange={(e) => setFormData({...formData, devoir_id: e.target.value})}
                                    required
                                    disabled={editingNote}
                                >
                                    <option value="">Sélectionner un devoir...</option>
                                    {devoirsDisponibles.map(devoir => (
                                        <option key={devoir.id} value={devoir.id}>
                                            {devoir.devoir_display}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="form-group">
                                <label>Matière *</label>
                                <select
                                    value={formData.matiere_id}
                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}
                                    required
                                    disabled={editingNote}
                                    style={{
                                        borderColor: formData.matiere_id ? '#28a745' : '#ced4da',
                                        borderWidth: '2px',
                                        borderStyle: 'solid'
                                    }}
                                >
                                    <option value="">Sélectionner une matière...</option>
                                    {matieres.length > 0 ? (
                                        matieres.map(matiere => (
                                            <option key={matiere.id || matiere.matiere_id} value={matiere.id || matiere.matiere_id}>
                                                📖 {matiere.nom || matiere.nom_matiere} | Filière: {matiere.filiere_nom || 'N/A'}
                                            </option>
                                        ))
                                    ) : (
                                        <option value="" disabled>Chargement des matières...</option>
                                    )}
                                </select>
                            </div>

                            {/* Panneau d'informations de l'étudiant sélectionné */}
                            {formData.etudiant_id && (
                                <div style={{
                                    backgroundColor: '#e8f4fd',
                                    border: '2px solid #007bff',
                                    borderRadius: '12px',
                                    padding: '15px',
                                    margin: '15px 0',
                                    boxShadow: '0 2px 8px rgba(0,123,255,0.1)'
                                }}>
                                    <h4 style={{
                                        color: '#007bff',
                                        margin: '0 0 10px 0',
                                        fontSize: '16px',
                                        fontWeight: 'bold',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px'
                                    }}>
                                        👤 Informations de l'étudiant
                                    </h4>
                                    {(() => {
                                        const etudiant = etudiants.find(e => (e.id || e.etudiant_id) == formData.etudiant_id);
                                        return etudiant ? (
                                            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>📚 Nom:</strong> {etudiant.nom || etudiant.nom_prenom}
                                                </p>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>👥 Groupe:</strong> {etudiant.groupe_nom || 'Non assigné'}
                                                </p>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>🏫 Classe:</strong> {etudiant.classe_nom || 'Non assignée'}
                                                </p>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>🎓 Filière:</strong> {etudiant.filiere_nom || 'Non spécifiée'}
                                                </p>
                                            </div>
                                        ) : (
                                            <p style={{ color: '#6c757d', fontStyle: 'italic' }}>
                                                Informations de l'étudiant non disponibles
                                            </p>
                                        );
                                    })()}
                                </div>
                            )}

                            {/* Panneau d'informations de la matière sélectionnée */}
                            {formData.matiere_id && (
                                <div style={{
                                    backgroundColor: '#f0f9ff',
                                    border: '2px solid #10b981',
                                    borderRadius: '12px',
                                    padding: '15px',
                                    margin: '15px 0',
                                    boxShadow: '0 2px 8px rgba(16,185,129,0.1)'
                                }}>
                                    <h4 style={{
                                        color: '#10b981',
                                        margin: '0 0 10px 0',
                                        fontSize: '16px',
                                        fontWeight: 'bold',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px'
                                    }}>
                                        📖 Informations de la matière
                                    </h4>
                                    {(() => {
                                        const matiere = matieres.find(m => (m.id || m.matiere_id) == formData.matiere_id);
                                        return matiere ? (
                                            <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>📚 Nom:</strong> {matiere.nom || matiere.nom_matiere}
                                                </p>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>🎓 Filière:</strong> {matiere.filiere_nom || 'Non spécifiée'}
                                                </p>
                                                <p style={{ margin: '5px 0' }}>
                                                    <strong>📊 Coefficient:</strong> {matiere.coefficient || 'Non défini'}
                                                </p>
                                            </div>
                                        ) : (
                                            <p style={{ color: '#6c757d', fontStyle: 'italic' }}>
                                                Informations de la matière non disponibles
                                            </p>
                                        );
                                    })()}
                                </div>
                            )}

                            <div className="form-group">
                                <label>Note (sur 20)</label>
                                <input
                                    type="number"
                                    min="0"
                                    max="20"
                                    step="0.01"
                                    value={formData.note}
                                    onChange={(e) => setFormData({...formData, note: e.target.value})}
                                    placeholder="Laisser vide pour calcul automatique"
                                />
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Si vide, la note sera calculée automatiquement à partir des réponses aux quiz.
                                </small>
                            </div>

                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingNote ? '💾 Modifier' : '➕ Créer'}
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingNote(null);
                                        resetForm();
                                    }}
                                >
                                    ❌ Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default NotesUnified;
