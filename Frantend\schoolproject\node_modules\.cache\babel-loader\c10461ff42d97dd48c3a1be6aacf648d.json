{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\dashboards\\\\ResponsableDashboard.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap, FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle, FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck, FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar, FaCertificate, FaClipboardList, FaQuestionCircle } from 'react-icons/fa';\nconst ResponsableDashboard = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    cours: 0,\n    matieres: 0,\n    groupes: 0,\n    etudiants: 0,\n    parents: 0,\n    filieres: 0,\n    utilisateurs: 0,\n    enseignants: 0,\n    classes: 0,\n    niveaux: 0,\n    absences_aujourdhui: 0,\n    retards_aujourdhui: 0,\n    devoirs_en_cours: 0,\n    quiz_actifs: 0,\n    factures_impayees: 0,\n    diplomes_annee: 0\n  });\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      // Simuler des données pour le moment\n      setStats({\n        totalEtudiants: 1250,\n        totalEnseignants: 85,\n        totalParents: 980,\n        totalClasses: 45,\n        absencesAujourdhui: 23,\n        coursAujourdhui: 156\n      });\n      setRecentActivities([{\n        id: 1,\n        type: 'inscription',\n        message: 'Nouvel étudiant inscrit - Jean Martin',\n        time: '10:30'\n      }, {\n        id: 2,\n        type: 'note',\n        message: 'Notes saisies pour la classe 3A - Mathématiques',\n        time: '09:15'\n      }, {\n        id: 3,\n        type: 'absence',\n        message: '5 absences signalées ce matin',\n        time: '08:45'\n      }, {\n        id: 4,\n        type: 'enseignant',\n        message: 'Nouveau professeur ajouté - Mme Dubois',\n        time: '08:00'\n      }]);\n      setAlertes([{\n        id: 1,\n        type: 'urgent',\n        message: 'Taux d\\'absence élevé en classe 2B',\n        priority: 'high'\n      }, {\n        id: 2,\n        type: 'info',\n        message: 'Réunion pédagogique prévue demain',\n        priority: 'medium'\n      }, {\n        id: 3,\n        type: 'maintenance',\n        message: 'Maintenance serveur programmée ce weekend',\n        priority: 'low'\n      }]);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  };\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'inscription':\n        return '👤';\n      case 'note':\n        return '📝';\n      case 'absence':\n        return '⚠️';\n      case 'enseignant':\n        return '👨‍🏫';\n      default:\n        return '📢';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return '#dc3545';\n      case 'medium':\n        return '#ffc107';\n      case 'low':\n        return '#28a745';\n      default:\n        return '#6c757d';\n    }\n  };\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    header: {\n      marginBottom: '30px'\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem'\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer'\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '50%',\n      color: 'white'\n    },\n    statContent: {\n      flex: 1\n    },\n    statNumber: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px'\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px'\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px'\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px'\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem'\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px'\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff'\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem'\n    },\n    activityContent: {\n      flex: 1\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px'\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem'\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid'\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333'\n    }\n  };\n  const statCards = [{\n    icon: FaGraduationCap,\n    number: stats.totalEtudiants,\n    label: 'Total Étudiants',\n    color: '#007bff',\n    link: '/etudiants'\n  }, {\n    icon: FaChalkboardTeacher,\n    number: stats.totalEnseignants,\n    label: 'Total Enseignants',\n    color: '#28a745',\n    link: '/enseignants'\n  }, {\n    icon: FaUserFriends,\n    number: stats.totalParents,\n    label: 'Total Parents',\n    color: '#ffc107',\n    link: '/parentes'\n  }, {\n    icon: FaUsers,\n    number: stats.totalClasses,\n    label: 'Total Classes',\n    color: '#17a2b8',\n    link: '/classes'\n  }, {\n    icon: FaExclamationTriangle,\n    number: stats.absencesAujourdhui,\n    label: \"Absences Aujourd'hui\",\n    color: '#dc3545'\n  }, {\n    icon: FaCalendarAlt,\n    number: stats.coursAujourdhui,\n    label: \"Cours Aujourd'hui\",\n    color: '#6f42c1'\n  }];\n  const quickActions = [{\n    icon: FaUsers,\n    title: 'Gérer les Utilisateurs',\n    description: 'Ajouter, modifier ou supprimer des utilisateurs',\n    link: '/registers'\n  }, {\n    icon: FaChalkboardTeacher,\n    title: 'Gérer les Cours',\n    description: 'Planifier et organiser les cours',\n    link: '/cours'\n  }, {\n    icon: FaGraduationCap,\n    title: 'Gérer les Classes',\n    description: 'Créer et organiser les classes',\n    link: '/classes'\n  }, {\n    icon: FaCog,\n    title: 'Configuration',\n    description: 'Paramètres système et configuration',\n    link: '/roles'\n  }];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.welcomeText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaUserTie, {\n    style: {\n      marginRight: '15px',\n      color: '#007bff'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 11\n    }\n  }), \"Bienvenue, \", (user === null || user === void 0 ? void 0 : user.email) || 'Responsable'), /*#__PURE__*/React.createElement(\"p\", {\n    style: styles.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 9\n    }\n  }, \"Tableau de bord administrateur - Vue d'ensemble de l'\\xE9cole\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }\n  }, statCards.map((stat, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: stat.link || '#',\n    style: {\n      textDecoration: 'none',\n      color: 'inherit'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.statIcon,\n      backgroundColor: stat.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(stat.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statNumber,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 17\n    }\n  }, stat.number), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 17\n    }\n  }, stat.label)))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.quickActionsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }\n  }, quickActions.map((action, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    to: action.link,\n    style: styles.actionCard,\n    onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n    onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(action.icon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 13\n    }\n  }, action.title), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.actionDescription,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 13\n    }\n  }, action.description)))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.contentGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaChartBar, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 13\n    }\n  }), \"Activit\\xE9s R\\xE9centes\"), recentActivities.map(activity => /*#__PURE__*/React.createElement(\"div\", {\n    key: activity.id,\n    style: styles.activityItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.activityIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 15\n    }\n  }, getActivityIcon(activity.type)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 17\n    }\n  }, activity.message), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.activityTime,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 17\n    }\n  }, activity.time))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.card,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    style: styles.cardTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaExclamationTriangle, {\n    style: styles.cardIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 13\n    }\n  }), \"Alertes & Notifications\"), alertes.map(alerte => /*#__PURE__*/React.createElement(\"div\", {\n    key: alerte.id,\n    style: {\n      ...styles.alertItem,\n      borderLeftColor: getPriorityColor(alerte.priority),\n      backgroundColor: `${getPriorityColor(alerte.priority)}10`\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.alertMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 15\n    }\n  }, alerte.message))))));\n};\nexport default ResponsableDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "Link", "axios", "FaUserTie", "FaUsers", "FaChalkboardTeacher", "FaGraduationCap", "FaUserFriends", "FaChartBar", "FaCog", "FaCalendarAlt", "FaExclamationTriangle", "FaBook", "FaBookOpen", "FaLayerGroup", "FaUserGraduate", "FaUserCheck", "FaStream", "FaIdCard", "FaSync", "FaTimes", "FaClock", "FaFileInvoiceDollar", "FaCertificate", "FaClipboardList", "FaQuestionCircle", "ResponsableDashboard", "user", "stats", "setStats", "cours", "matieres", "groupes", "etudiants", "parents", "filieres", "utilisateurs", "enseignants", "classes", "niveaux", "absences_aujourdhui", "retards_aujourdhui", "devoirs_en_cours", "quiz_actifs", "factures_impayees", "diplomes_annee", "recentActivities", "setRecentActivities", "alertes", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "fetchDashboardData", "totalEtudiants", "totalEnseignants", "totalParents", "totalClasses", "absences<PERSON><PERSON><PERSON><PERSON><PERSON>", "coursAujourdhui", "id", "type", "message", "time", "priority", "console", "getActivityIcon", "getPriorityColor", "styles", "container", "padding", "backgroundColor", "minHeight", "header", "marginBottom", "welcomeText", "fontSize", "fontWeight", "color", "subtitle", "statsGrid", "display", "gridTemplateColumns", "gap", "statCard", "borderRadius", "boxShadow", "alignItems", "transition", "cursor", "statIcon", "marginRight", "statContent", "flex", "statNumber", "statLabel", "quickActionsGrid", "actionCard", "textAlign", "textDecoration", "actionIcon", "actionTitle", "actionDescription", "contentGrid", "card", "cardTitle", "cardIcon", "activityItem", "borderBottom", "activityIcon", "activityContent", "activityMessage", "activityTime", "alertItem", "borderLeft", "alertMessage", "statCards", "icon", "number", "label", "link", "quickActions", "title", "description", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "map", "stat", "index", "key", "to", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "action", "activity", "alerte", "borderLeftColor"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/dashboards/ResponsableDashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap,\n  FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle,\n  FaBook, FaBookOpen, FaLayerGroup, FaUserGraduate, FaUserCheck,\n  FaStream, FaIdCard, FaSync, FaTimes, FaClock, FaFileInvoiceDollar,\n  FaCertificate, FaClipboardList, FaQuestionCircle\n} from 'react-icons/fa';\n\nconst ResponsableDashboard = () => {\n  const { user } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    cours: 0,\n    matieres: 0,\n    groupes: 0,\n    etudiants: 0,\n    parents: 0,\n    filieres: 0,\n    utilisateurs: 0,\n    enseignants: 0,\n    classes: 0,\n    niveaux: 0,\n    absences_aujourdhui: 0,\n    retards_aujourdhui: 0,\n    devoirs_en_cours: 0,\n    quiz_actifs: 0,\n    factures_impayees: 0,\n    diplomes_annee: 0\n  });\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [alertes, setAlertes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      // Simuler des données pour le moment\n      setStats({\n        totalEtudiants: 1250,\n        totalEnseignants: 85,\n        totalParents: 980,\n        totalClasses: 45,\n        absencesAujourdhui: 23,\n        coursAujourdhui: 156\n      });\n\n      setRecentActivities([\n        { id: 1, type: 'inscription', message: 'Nouvel étudiant inscrit - Jean Martin', time: '10:30' },\n        { id: 2, type: 'note', message: 'Notes saisies pour la classe 3A - Mathématiques', time: '09:15' },\n        { id: 3, type: 'absence', message: '5 absences signalées ce matin', time: '08:45' },\n        { id: 4, type: 'enseignant', message: 'Nouveau professeur ajouté - Mme Dubois', time: '08:00' }\n      ]);\n\n      setAlertes([\n        { id: 1, type: 'urgent', message: 'Taux d\\'absence élevé en classe 2B', priority: 'high' },\n        { id: 2, type: 'info', message: 'Réunion pédagogique prévue demain', priority: 'medium' },\n        { id: 3, type: 'maintenance', message: 'Maintenance serveur programmée ce weekend', priority: 'low' }\n      ]);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  };\n\n  const getActivityIcon = (type) => {\n    switch(type) {\n      case 'inscription': return '👤';\n      case 'note': return '📝';\n      case 'absence': return '⚠️';\n      case 'enseignant': return '👨‍🏫';\n      default: return '📢';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch(priority) {\n      case 'high': return '#dc3545';\n      case 'medium': return '#ffc107';\n      case 'low': return '#28a745';\n      default: return '#6c757d';\n    }\n  };\n\n  const styles = {\n    container: {\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh',\n    },\n    header: {\n      marginBottom: '30px',\n    },\n    welcomeText: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    subtitle: {\n      color: '#666',\n      fontSize: '1.1rem',\n    },\n    statsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    statCard: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      display: 'flex',\n      alignItems: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n    },\n    statIcon: {\n      fontSize: '2.5rem',\n      marginRight: '20px',\n      padding: '15px',\n      borderRadius: '50%',\n      color: 'white',\n    },\n    statContent: {\n      flex: 1,\n    },\n    statNumber: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '5px',\n    },\n    statLabel: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    quickActionsGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n      gap: '20px',\n      marginBottom: '30px',\n    },\n    actionCard: {\n      backgroundColor: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      textAlign: 'center',\n      transition: 'transform 0.3s ease',\n      cursor: 'pointer',\n      textDecoration: 'none',\n      color: 'inherit',\n    },\n    actionIcon: {\n      fontSize: '2rem',\n      color: '#007bff',\n      marginBottom: '15px',\n    },\n    actionTitle: {\n      fontSize: '1.1rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '10px',\n    },\n    actionDescription: {\n      color: '#666',\n      fontSize: '0.9rem',\n    },\n    contentGrid: {\n      display: 'grid',\n      gridTemplateColumns: '2fr 1fr',\n      gap: '30px',\n    },\n    card: {\n      backgroundColor: 'white',\n      padding: '25px',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n    },\n    cardTitle: {\n      fontSize: '1.3rem',\n      fontWeight: 'bold',\n      color: '#333',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    cardIcon: {\n      marginRight: '10px',\n      color: '#007bff',\n    },\n    activityItem: {\n      padding: '12px',\n      borderBottom: '1px solid #eee',\n      display: 'flex',\n      alignItems: 'center',\n    },\n    activityIcon: {\n      marginRight: '15px',\n      fontSize: '1.2rem',\n    },\n    activityContent: {\n      flex: 1,\n    },\n    activityMessage: {\n      color: '#333',\n      fontSize: '0.9rem',\n      marginBottom: '4px',\n    },\n    activityTime: {\n      color: '#999',\n      fontSize: '0.8rem',\n    },\n    alertItem: {\n      padding: '15px',\n      borderRadius: '8px',\n      marginBottom: '15px',\n      borderLeft: '4px solid',\n    },\n    alertMessage: {\n      fontWeight: 'bold',\n      color: '#333',\n    },\n  };\n\n  const statCards = [\n    { \n      icon: FaGraduationCap, \n      number: stats.totalEtudiants, \n      label: 'Total Étudiants', \n      color: '#007bff',\n      link: '/etudiants'\n    },\n    { \n      icon: FaChalkboardTeacher, \n      number: stats.totalEnseignants, \n      label: 'Total Enseignants', \n      color: '#28a745',\n      link: '/enseignants'\n    },\n    { \n      icon: FaUserFriends, \n      number: stats.totalParents, \n      label: 'Total Parents', \n      color: '#ffc107',\n      link: '/parentes'\n    },\n    { \n      icon: FaUsers, \n      number: stats.totalClasses, \n      label: 'Total Classes', \n      color: '#17a2b8',\n      link: '/classes'\n    },\n    { \n      icon: FaExclamationTriangle, \n      number: stats.absencesAujourdhui, \n      label: \"Absences Aujourd'hui\", \n      color: '#dc3545'\n    },\n    { \n      icon: FaCalendarAlt, \n      number: stats.coursAujourdhui, \n      label: \"Cours Aujourd'hui\", \n      color: '#6f42c1'\n    },\n  ];\n\n  const quickActions = [\n    { icon: FaUsers, title: 'Gérer les Utilisateurs', description: 'Ajouter, modifier ou supprimer des utilisateurs', link: '/registers' },\n    { icon: FaChalkboardTeacher, title: 'Gérer les Cours', description: 'Planifier et organiser les cours', link: '/cours' },\n    { icon: FaGraduationCap, title: 'Gérer les Classes', description: 'Créer et organiser les classes', link: '/classes' },\n    { icon: FaCog, title: 'Configuration', description: 'Paramètres système et configuration', link: '/roles' },\n  ];\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête */}\n      <div style={styles.header}>\n        <h1 style={styles.welcomeText}>\n          <FaUserTie style={{ marginRight: '15px', color: '#007bff' }} />\n          Bienvenue, {user?.email || 'Responsable'}\n        </h1>\n        <p style={styles.subtitle}>Tableau de bord administrateur - Vue d'ensemble de l'école</p>\n      </div>\n\n      {/* Statistiques */}\n      <div style={styles.statsGrid}>\n        {statCards.map((stat, index) => (\n          <Link\n            key={index}\n            to={stat.link || '#'}\n            style={{ textDecoration: 'none', color: 'inherit' }}\n          >\n            <div \n              style={styles.statCard}\n              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{...styles.statIcon, backgroundColor: stat.color}}>\n                <stat.icon />\n              </div>\n              <div style={styles.statContent}>\n                <div style={styles.statNumber}>{stat.number}</div>\n                <div style={styles.statLabel}>{stat.label}</div>\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Actions rapides */}\n      <div style={styles.quickActionsGrid}>\n        {quickActions.map((action, index) => (\n          <Link\n            key={index}\n            to={action.link}\n            style={styles.actionCard}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n          >\n            <div style={styles.actionIcon}>\n              <action.icon />\n            </div>\n            <div style={styles.actionTitle}>{action.title}</div>\n            <div style={styles.actionDescription}>{action.description}</div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Contenu principal */}\n      <div style={styles.contentGrid}>\n        {/* Activités récentes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaChartBar style={styles.cardIcon} />\n            Activités Récentes\n          </h2>\n          {recentActivities.map(activity => (\n            <div key={activity.id} style={styles.activityItem}>\n              <span style={styles.activityIcon}>\n                {getActivityIcon(activity.type)}\n              </span>\n              <div style={styles.activityContent}>\n                <div style={styles.activityMessage}>{activity.message}</div>\n                <div style={styles.activityTime}>{activity.time}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Alertes */}\n        <div style={styles.card}>\n          <h2 style={styles.cardTitle}>\n            <FaExclamationTriangle style={styles.cardIcon} />\n            Alertes & Notifications\n          </h2>\n          {alertes.map(alerte => (\n            <div \n              key={alerte.id} \n              style={{\n                ...styles.alertItem,\n                borderLeftColor: getPriorityColor(alerte.priority),\n                backgroundColor: `${getPriorityColor(alerte.priority)}10`\n              }}\n            >\n              <div style={styles.alertMessage}>{alerte.message}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResponsableDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,EACxDC,aAAa,EAAEC,UAAU,EAAEC,KAAK,EAAEC,aAAa,EAAEC,qBAAqB,EACtEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAC7DC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EACjEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAC3C,gBAAgB;AAEvB,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAG5B,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC;IACjCiC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,mBAAmB,EAAE,CAAC;IACtBC,kBAAkB,EAAE,CAAC;IACrBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdwD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACAzB,QAAQ,CAAC;QACP0B,cAAc,EAAE,IAAI;QACpBC,gBAAgB,EAAE,EAAE;QACpBC,YAAY,EAAE,GAAG;QACjBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,eAAe,EAAE;MACnB,CAAC,CAAC;MAEFb,mBAAmB,CAAC,CAClB;QAAEc,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE,uCAAuC;QAAEC,IAAI,EAAE;MAAQ,CAAC,EAC/F;QAAEH,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE,iDAAiD;QAAEC,IAAI,EAAE;MAAQ,CAAC,EAClG;QAAEH,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE,+BAA+B;QAAEC,IAAI,EAAE;MAAQ,CAAC,EACnF;QAAEH,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAE,wCAAwC;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAChG,CAAC;MAEFf,UAAU,CAAC,CACT;QAAEY,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,QAAQ;QAAEC,OAAO,EAAE,oCAAoC;QAAEE,QAAQ,EAAE;MAAO,CAAC,EAC1F;QAAEJ,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE,mCAAmC;QAAEE,QAAQ,EAAE;MAAS,CAAC,EACzF;QAAEJ,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE,2CAA2C;QAAEE,QAAQ,EAAE;MAAM,CAAC,CACtG,CAAC;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC;EAED,MAAMe,eAAe,GAAIL,IAAI,IAAK;IAChC,QAAOA,IAAI;MACT,KAAK,aAAa;QAAE,OAAO,IAAI;MAC/B,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,SAAS;QAAE,OAAO,IAAI;MAC3B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAIH,QAAQ,IAAK;IACrC,QAAOA,QAAQ;MACb,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMI,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACNC,YAAY,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE;MACXC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDK,QAAQ,EAAE;MACRD,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDI,SAAS,EAAE;MACTC,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDU,QAAQ,EAAE;MACRb,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCL,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,qBAAqB;MACjCC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACRd,QAAQ,EAAE,QAAQ;MAClBe,WAAW,EAAE,MAAM;MACnBrB,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,KAAK;MACnBP,KAAK,EAAE;IACT,CAAC;IACDc,WAAW,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVlB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACDqB,SAAS,EAAE;MACTjB,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDoB,gBAAgB,EAAE;MAChBf,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,sCAAsC;MAC3DC,GAAG,EAAE,MAAM;MACXT,YAAY,EAAE;IAChB,CAAC;IACDuB,UAAU,EAAE;MACV1B,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCY,SAAS,EAAE,QAAQ;MACnBV,UAAU,EAAE,qBAAqB;MACjCC,MAAM,EAAE,SAAS;MACjBU,cAAc,EAAE,MAAM;MACtBrB,KAAK,EAAE;IACT,CAAC;IACDsB,UAAU,EAAE;MACVxB,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAE,SAAS;MAChBJ,YAAY,EAAE;IAChB,CAAC;IACD2B,WAAW,EAAE;MACXzB,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE;IAChB,CAAC;IACD4B,iBAAiB,EAAE;MACjBxB,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACD2B,WAAW,EAAE;MACXtB,OAAO,EAAE,MAAM;MACfC,mBAAmB,EAAE,SAAS;MAC9BC,GAAG,EAAE;IACP,CAAC;IACDqB,IAAI,EAAE;MACJjC,eAAe,EAAE,OAAO;MACxBD,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE;IACb,CAAC;IACDmB,SAAS,EAAE;MACT7B,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbJ,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE;IACd,CAAC;IACDmB,QAAQ,EAAE;MACRf,WAAW,EAAE,MAAM;MACnBb,KAAK,EAAE;IACT,CAAC;IACD6B,YAAY,EAAE;MACZrC,OAAO,EAAE,MAAM;MACfsC,YAAY,EAAE,gBAAgB;MAC9B3B,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE;IACd,CAAC;IACDsB,YAAY,EAAE;MACZlB,WAAW,EAAE,MAAM;MACnBf,QAAQ,EAAE;IACZ,CAAC;IACDkC,eAAe,EAAE;MACfjB,IAAI,EAAE;IACR,CAAC;IACDkB,eAAe,EAAE;MACfjC,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE,QAAQ;MAClBF,YAAY,EAAE;IAChB,CAAC;IACDsC,YAAY,EAAE;MACZlC,KAAK,EAAE,MAAM;MACbF,QAAQ,EAAE;IACZ,CAAC;IACDqC,SAAS,EAAE;MACT3C,OAAO,EAAE,MAAM;MACfe,YAAY,EAAE,KAAK;MACnBX,YAAY,EAAE,MAAM;MACpBwC,UAAU,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACZtC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EAED,MAAMsC,SAAS,GAAG,CAChB;IACEC,IAAI,EAAEhH,eAAe;IACrBiH,MAAM,EAAE3F,KAAK,CAAC2B,cAAc;IAC5BiE,KAAK,EAAE,iBAAiB;IACxBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAEjH,mBAAmB;IACzBkH,MAAM,EAAE3F,KAAK,CAAC4B,gBAAgB;IAC9BgE,KAAK,EAAE,mBAAmB;IAC1BzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAE/G,aAAa;IACnBgH,MAAM,EAAE3F,KAAK,CAAC6B,YAAY;IAC1B+D,KAAK,EAAE,eAAe;IACtBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAElH,OAAO;IACbmH,MAAM,EAAE3F,KAAK,CAAC8B,YAAY;IAC1B8D,KAAK,EAAE,eAAe;IACtBzC,KAAK,EAAE,SAAS;IAChB0C,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAE3G,qBAAqB;IAC3B4G,MAAM,EAAE3F,KAAK,CAAC+B,kBAAkB;IAChC6D,KAAK,EAAE,sBAAsB;IAC7BzC,KAAK,EAAE;EACT,CAAC,EACD;IACEuC,IAAI,EAAE5G,aAAa;IACnB6G,MAAM,EAAE3F,KAAK,CAACgC,eAAe;IAC7B4D,KAAK,EAAE,mBAAmB;IAC1BzC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM2C,YAAY,GAAG,CACnB;IAAEJ,IAAI,EAAElH,OAAO;IAAEuH,KAAK,EAAE,wBAAwB;IAAEC,WAAW,EAAE,iDAAiD;IAAEH,IAAI,EAAE;EAAa,CAAC,EACtI;IAAEH,IAAI,EAAEjH,mBAAmB;IAAEsH,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,kCAAkC;IAAEH,IAAI,EAAE;EAAS,CAAC,EACxH;IAAEH,IAAI,EAAEhH,eAAe;IAAEqH,KAAK,EAAE,mBAAmB;IAAEC,WAAW,EAAE,gCAAgC;IAAEH,IAAI,EAAE;EAAW,CAAC,EACtH;IAAEH,IAAI,EAAE7G,KAAK;IAAEkH,KAAK,EAAE,eAAe;IAAEC,WAAW,EAAE,qCAAqC;IAAEH,IAAI,EAAE;EAAS,CAAC,CAC5G;EAED,oBACE7H,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACC,SAAU;IAAAyD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACK,MAAO;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBxI,KAAA,CAAAiI,aAAA;IAAIC,KAAK,EAAEzD,MAAM,CAACO,WAAY;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BxI,KAAA,CAAAiI,aAAA,CAAC1H,SAAS;IAAC2H,KAAK,EAAE;MAAElC,WAAW,EAAE,MAAM;MAAEb,KAAK,EAAE;IAAU,CAAE;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACpD,EAAC,CAAAzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,KAAK,KAAI,aACzB,CAAC,eACLzI,KAAA,CAAAiI,aAAA;IAAGC,KAAK,EAAEzD,MAAM,CAACW,QAAS;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+DAA6D,CACrF,CAAC,eAGNxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACY,SAAU;IAAA8C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1Bf,SAAS,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzB5I,KAAA,CAAAiI,aAAA,CAAC5H,IAAI;IACHwI,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEH,IAAI,CAACd,IAAI,IAAI,GAAI;IACrBK,KAAK,EAAE;MAAE1B,cAAc,EAAE,MAAM;MAAErB,KAAK,EAAE;IAAU,CAAE;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpDxI,KAAA,CAAAiI,aAAA;IACEC,KAAK,EAAEzD,MAAM,CAACgB,QAAS;IACvBsD,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,eAAgB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvExI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAE;MAAC,GAAGzD,MAAM,CAACsB,QAAQ;MAAEnB,eAAe,EAAE+D,IAAI,CAACxD;IAAK,CAAE;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5DxI,KAAA,CAAAiI,aAAA,CAACU,IAAI,CAACjB,IAAI;IAAAS,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACT,CAAC,eACNxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACwB,WAAY;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC0B,UAAW;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEG,IAAI,CAAChB,MAAY,CAAC,eAClD3H,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC2B,SAAU;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEG,IAAI,CAACf,KAAW,CAC5C,CACF,CACD,CACP,CACE,CAAC,eAGN5H,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC4B,gBAAiB;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCV,YAAY,CAACY,GAAG,CAAC,CAACU,MAAM,EAAER,KAAK,kBAC9B5I,KAAA,CAAAiI,aAAA,CAAC5H,IAAI;IACHwI,GAAG,EAAED,KAAM;IACXE,EAAE,EAAEM,MAAM,CAACvB,IAAK;IAChBK,KAAK,EAAEzD,MAAM,CAAC6B,UAAW;IACzByC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,kBAAmB;IAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACf,KAAK,CAACgB,SAAS,GAAG,eAAgB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvExI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACgC,UAAW;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BxI,KAAA,CAAAiI,aAAA,CAACmB,MAAM,CAAC1B,IAAI;IAAAS,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACX,CAAC,eACNxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACiC,WAAY;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEY,MAAM,CAACrB,KAAW,CAAC,eACpD/H,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACkC,iBAAkB;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEY,MAAM,CAACpB,WAAiB,CAC3D,CACP,CACE,CAAC,eAGNhI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACmC,WAAY;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7BxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACoC,IAAK;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBxI,KAAA,CAAAiI,aAAA;IAAIC,KAAK,EAAEzD,MAAM,CAACqC,SAAU;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxI,KAAA,CAAAiI,aAAA,CAACrH,UAAU;IAACsH,KAAK,EAAEzD,MAAM,CAACsC,QAAS;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,4BAEpC,CAAC,EACJtF,gBAAgB,CAACwF,GAAG,CAACW,QAAQ,iBAC5BrJ,KAAA,CAAAiI,aAAA;IAAKY,GAAG,EAAEQ,QAAQ,CAACpF,EAAG;IAACiE,KAAK,EAAEzD,MAAM,CAACuC,YAAa;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChDxI,KAAA,CAAAiI,aAAA;IAAMC,KAAK,EAAEzD,MAAM,CAACyC,YAAa;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BjE,eAAe,CAAC8E,QAAQ,CAACnF,IAAI,CAC1B,CAAC,eACPlE,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC0C,eAAgB;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC2C,eAAgB;IAAAe,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEa,QAAQ,CAAClF,OAAa,CAAC,eAC5DnE,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC4C,YAAa;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEa,QAAQ,CAACjF,IAAU,CAClD,CACF,CACN,CACE,CAAC,eAGNpE,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAACoC,IAAK;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBxI,KAAA,CAAAiI,aAAA;IAAIC,KAAK,EAAEzD,MAAM,CAACqC,SAAU;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxI,KAAA,CAAAiI,aAAA,CAAClH,qBAAqB;IAACmH,KAAK,EAAEzD,MAAM,CAACsC,QAAS;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,2BAE/C,CAAC,EACJpF,OAAO,CAACsF,GAAG,CAACY,MAAM,iBACjBtJ,KAAA,CAAAiI,aAAA;IACEY,GAAG,EAAES,MAAM,CAACrF,EAAG;IACfiE,KAAK,EAAE;MACL,GAAGzD,MAAM,CAAC6C,SAAS;MACnBiC,eAAe,EAAE/E,gBAAgB,CAAC8E,MAAM,CAACjF,QAAQ,CAAC;MAClDO,eAAe,EAAE,GAAGJ,gBAAgB,CAAC8E,MAAM,CAACjF,QAAQ,CAAC;IACvD,CAAE;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFxI,KAAA,CAAAiI,aAAA;IAAKC,KAAK,EAAEzD,MAAM,CAAC+C,YAAa;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEc,MAAM,CAACnF,OAAa,CACnD,CACN,CACE,CACF,CACF,CAAC;AAEV,CAAC;AAED,eAAerC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}